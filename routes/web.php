<?php

declare(strict_types=1);

use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\PublicProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
| Routes accessible to all users without authentication
*/

Route::get('/', [HomeController::class, 'index'])->name('home');

// Public property browsing
Route::get('/properties', [PropertyController::class, 'index'])->name('properties.index');
Route::get('/properties/{property}', [PropertyController::class, 'show'])->name('properties.show');

// Public User Profile
Route::get('/profile/{user}', [PublicProfileController::class, 'show'])->name('public.profile.show');

/*
|--------------------------------------------------------------------------
| Authenticated User Routes
|--------------------------------------------------------------------------
| Routes requiring authentication but no specific role
*/

Route::middleware(['auth', 'verified'])->group(function (): void {
    // Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Profile Management (consolidated single route pattern)
    Route::prefix('profile')->name('profile.')->group(function (): void {
        Route::get('/', [ProfileController::class, 'edit'])->name('edit');
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
    });

    // User Settings
    Route::prefix('settings')->name('settings.')->group(function (): void {
        Route::get('/', [ProfileController::class, 'edit'])->name('index');
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile');
        Route::get('/password', function () {
            return view('livewire.settings.password');
        })->name('password');
        Route::get('/appearance', function () {
            return view('livewire.settings.appearance');
        })->name('appearance');
        Route::get('/notifications', function () {
            return view('settings.notifications');
        })->name('notifications');
        Route::get('/privacy', function () {
            return view('settings.privacy');
        })->name('privacy');
    });
});

/*
|--------------------------------------------------------------------------
| Agent Routes
|--------------------------------------------------------------------------
| Routes for property agents (formerly listers)
*/

Route::middleware(['auth', 'verified', 'role:agent'])->prefix('agent')->name('agent.')->group(function (): void {
    // Property Management - Standardized on Livewire components
    Route::prefix('properties')->name('properties.')->group(function (): void {
        Route::get('/', App\Livewire\MyProperties::class)->name('index');
        Route::get('/create', App\Livewire\CreateProperty::class)->name('create');
        Route::get('/{property}/edit', App\Livewire\EditProperty::class)->name('edit');
    });
});

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
| Routes for administrative functions
*/

Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function (): void {
    // Admin Dashboard
    Route::get('/', App\Livewire\Admin\Dashboard::class)->name('dashboard');

    // User Management
    Route::prefix('users')->name('users.')->group(function (): void {
        Route::get('/', App\Livewire\Admin\UserManagement::class)->name('index');
    });

    // Property Management - Standardized on Livewire components
    Route::prefix('properties')->name('properties.')->group(function (): void {
        Route::get('/', App\Livewire\Admin\PropertyManagement::class)->name('index');
        Route::get('/{property}/edit', App\Livewire\EditProperty::class)->name('edit');
    });

    // Favorites Management
    Route::get('/favorites', App\Livewire\Admin\FavoriteManager::class)->name('favorites');

    // Reports
    Route::prefix('reports')->name('reports.')->group(function (): void {
        Route::get('/users', App\Livewire\Admin\UserReports::class)->name('users');
        Route::get('/properties', App\Livewire\Admin\PropertyReports::class)->name('properties');
    });

    Route::prefix('settings')->name('settings.')->group(function (): void {
        Route::get('/', App\Livewire\Admin\Settings::class)->name('index');
        Route::get('/security', function () {
            return view('admin.settings.security');
        })->name('security');
        Route::get('/health', function () {
            return view('admin.settings.health');
        })->name('health');
    });

    // Media Management
    Route::get('/media-settings', App\Livewire\Admin\MediaSettings::class)->name('media-settings');
});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
| Include Laravel's authentication routes (login, register, etc.)
*/

require __DIR__.'/auth.php';
