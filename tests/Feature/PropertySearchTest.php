<?php

declare(strict_types=1);

use App\Livewire\PropertySearch;
use App\Models\Property;
use App\Models\PropertySubType;
use App\Models\PropertyType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Create roles first
    Spatie\Permission\Models\Role::create(['name' => 'agent']);

    // Create test users with roles
    $this->agent = User::factory()->create();
    $this->agent->assignRole('agent');

    // Create property types and subtypes
    $this->propertyType = PropertyType::create(['name' => 'House']);
    $this->propertySubType = PropertySubType::create([
        'name' => 'Single Family',
        'property_type_id' => $this->propertyType->id,
    ]);

    // Create test properties
    $this->property1 = Property::factory()->create([
        'title' => 'Beautiful Downtown House',
        'city' => 'New York',
        'price' => 500000,
        'listing_type' => 'for_sale',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);

    $this->property2 = Property::factory()->create([
        'title' => 'Suburban Family Home',
        'city' => 'Los Angeles',
        'price' => 300000,
        'listing_type' => 'for_sale',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);
});

test('property search component renders correctly', function (): void {
    Livewire::test(PropertySearch::class)
        ->assertStatus(200)
        ->assertViewIs('livewire.property-search')
        ->assertSee('Beautiful Downtown House')
        ->assertSee('Suburban Family Home');
});

test('keyword search filters properties', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('keywords', 'Downtown')
        ->assertSee('Beautiful Downtown House')
        ->assertDontSee('Suburban Family Home');
});

test('property type filter works', function (): void {
    // Create another property type
    $apartmentType = PropertyType::factory()->create(['name' => 'Apartment']);
    $apartmentSubType = PropertySubType::factory()->create([
        'name' => 'Studio',
        'property_type_id' => $apartmentType->id,
    ]);

    $apartment = Property::factory()->create([
        'title' => 'Modern Apartment',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $apartmentSubType->id,
        'status' => 'published',
    ]);

    Livewire::test(PropertySearch::class)
        ->set('property_type_id', $this->propertyType->id)
        ->assertSee('Beautiful Downtown House')
        ->assertSee('Suburban Family Home')
        ->assertDontSee('Modern Apartment');
});

test('price range filter works', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('min_price', 400000)
        ->set('max_price', 600000)
        ->assertSee('Beautiful Downtown House')
        ->assertDontSee('Suburban Family Home');
});

test('listing type filter works', function (): void {
    // Create a rental property
    $rental = Property::factory()->create([
        'title' => 'Rental Property',
        'listing_type' => 'for_rent',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);

    Livewire::test(PropertySearch::class)
        ->set('listing_type', 'for_sale')
        ->assertSee('Beautiful Downtown House')
        ->assertSee('Suburban Family Home')
        ->assertDontSee('Rental Property');
});

test('property type change resets sub type', function (): void {
    // Create another property type
    $apartmentType = PropertyType::factory()->create(['name' => 'Apartment']);

    Livewire::test(PropertySearch::class)
        ->set('property_type_id', $this->propertyType->id)
        ->set('property_sub_type_id', $this->propertySubType->id)
        ->set('property_type_id', $apartmentType->id)
        ->assertSet('property_sub_type_id', '');
});

test('reset filters clears all filters', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('keywords', 'Downtown')
        ->set('property_type_id', $this->propertyType->id)
        ->set('listing_type', 'for_sale')
        ->set('min_price', 100000)
        ->set('max_price', 500000)
        ->call('resetFilters')
        ->assertSet('keywords', '')
        ->assertSet('property_type_id', '')
        ->assertSet('listing_type', '')
        ->assertSet('min_price', '')
        ->assertSet('max_price', '');
});

test('show property redirects correctly', function (): void {
    Livewire::test(PropertySearch::class)
        ->call('showProperty', $this->property1->id)
        ->assertRedirect(route('properties.show', $this->property1->id));
});

test('pagination resets when filters change', function (): void {
    // Create enough properties to trigger pagination
    Property::factory(15)->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);

    $component = Livewire::test(PropertySearch::class);

    // Go to page 2 using the nextPage method
    $component->call('nextPage');

    // Change a filter - should reset to page 1
    $component->set('keywords', 'test');

    // Verify we're back on page 1 by checking the component state
    $component->assertSet('keywords', 'test');
});

test('sort functionality works correctly', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('sort_by', 'price')
        ->set('sort_direction', 'asc')
        ->assertSee('Suburban Family Home') // Lower price should appear first
        ->set('sort_direction', 'desc')
        ->assertSee('Beautiful Downtown House'); // Higher price should appear first
});

test('total properties count is calculated correctly', function (): void {
    $component = Livewire::test(PropertySearch::class);

    // Should show total count of published properties
    expect($component->get('total_properties'))->toBeGreaterThan(0);

    // Filter should update count
    $component->set('keywords', 'Downtown');
    expect($component->get('total_properties'))->toBe(1);
});

test('properties updated event is dispatched', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('keywords', 'Downtown')
        ->assertDispatched('propertiesUpdated');
});

test('query string parameters are maintained', function (): void {
    Livewire::test(PropertySearch::class)
        ->set('keywords', 'Downtown')
        ->set('property_type_id', $this->propertyType->id)
        ->assertSet('keywords', 'Downtown')
        ->assertSet('property_type_id', $this->propertyType->id);
});

test('only published properties are shown', function (): void {
    // Create a draft property
    $draft = Property::factory()->create([
        'title' => 'Draft Property',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'draft',
    ]);

    Livewire::test(PropertySearch::class)
        ->assertSee('Beautiful Downtown House')
        ->assertDontSee('Draft Property');
});
