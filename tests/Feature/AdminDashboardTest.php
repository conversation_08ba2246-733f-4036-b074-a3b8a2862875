<?php

declare(strict_types=1);

use App\Models\Property;
use App\Models\User;
use Spatie\Permission\Models\Role;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function (): void {
    // Create roles for testing
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'agent']);
    Role::create(['name' => 'seeker']);
});

test('admin can access admin dashboard', function (): void {
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    $response->assertSee('Admin Dashboard');
    $response->assertSee('Total Users');
    $response->assertSee('Total Properties');
});

test('non-admin users cannot access admin dashboard', function (): void {
    $user = User::factory()->create();
    $user->assignRole('seeker');

    $this->actingAs($user);

    $response = $this->get('/admin');
    $response->assertStatus(403);
});

test('admin dashboard shows correct statistics', function (): void {
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    // Create test data
    $agents = User::factory()->count(5)->create();
    foreach ($agents as $agent) {
        $agent->assignRole('agent');
    }

    $seekers = User::factory()->count(10)->create();
    foreach ($seekers as $seeker) {
        $seeker->assignRole('seeker');
    }

    $testAgent = User::factory()->create();
    $testAgent->assignRole('agent');

    Property::factory()->count(3)->create([
        'user_id' => $testAgent->id,
        'status' => 'published',
    ]);
    Property::factory()->count(2)->create([
        'user_id' => $testAgent->id,
        'status' => 'draft',
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check if statistics are displayed
    $response->assertSee('16'); // Total users (admin + 5 agents + 10 seekers)
    $response->assertSee('5'); // Total properties
    $response->assertSee('3'); // Published properties
    $response->assertSee('2'); // Pending properties
});

test('admin dashboard shows user role breakdown', function (): void {
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    // Create users with different roles
    $admins = User::factory()->count(2)->create();
    foreach ($admins as $adminUser) {
        $adminUser->assignRole('admin');
    }

    $agents = User::factory()->count(3)->create();
    foreach ($agents as $agent) {
        $agent->assignRole('agent');
    }

    $seekers = User::factory()->count(5)->create();
    foreach ($seekers as $seeker) {
        $seeker->assignRole('seeker');
    }

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check role breakdown
    $response->assertSee('3'); // Admin users (including the test admin)
    $response->assertSee('3'); // agent users
    $response->assertSee('5'); // Seeker users
});

test('admin dashboard shows recent activity', function (): void {
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    $agent = User::factory()->create([
        'name' => 'Test agent',
    ]);
    $agent->assignRole('agent');

    $property = Property::factory()->create([
        'user_id' => $agent->id,
        'title' => 'Test Property',
        'status' => 'published',
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);

    // Check if recent activity is shown
    $response->assertSee('Recent Properties');
    $response->assertSee('Test Property');
    $response->assertSee('Test agent');
});
