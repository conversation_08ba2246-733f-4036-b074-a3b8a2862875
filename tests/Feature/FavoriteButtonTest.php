<?php

declare(strict_types=1);

use App\Models\Property;
use App\Models\User;
use Livewire\Livewire;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

test('user can favorite a property', function (): void {
    $user = User::factory()->create();
    $property = Property::factory()->create();

    $this->actingAs($user);

    Livewire::test(App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', false)
        ->call('toggleFavorite')
        ->assertSet('isFavorited', true);

    expect($user->favoritedProperties()->where('property_id', $property->id)->exists())->toBeTrue();
});

test('user can unfavorite a property', function (): void {
    $user = User::factory()->create();
    $property = Property::factory()->create();

    // First favorite the property
    $user->favoritedProperties()->attach($property->id);

    $this->actingAs($user);

    Livewire::test(App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', true)
        ->call('toggleFavorite')
        ->assertSet('isFavorited', false);

    expect($user->favoritedProperties()->where('property_id', $property->id)->exists())->toBeFalse();
});

test('guest user is redirected to login when trying to favorite', function (): void {
    $property = Property::factory()->create();

    Livewire::test(App\Livewire\FavoriteButton::class, ['property' => $property])
        ->assertSet('isFavorited', false)
        ->call('toggleFavorite')
        ->assertRedirect(route('login'));
});
