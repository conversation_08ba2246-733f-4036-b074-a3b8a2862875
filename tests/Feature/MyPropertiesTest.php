<?php

declare(strict_types=1);

use App\Livewire\MyProperties;
use App\Models\Property;
use App\Models\PropertySubType;
use App\Models\PropertyType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Create roles first
    Spatie\Permission\Models\Role::create(['name' => 'agent']);
    Spatie\Permission\Models\Role::create(['name' => 'seeker']);

    // Create test users with roles
    $this->agent = User::factory()->create();
    $this->agent->assignRole('agent');

    $this->otherAgent = User::factory()->create();
    $this->otherAgent->assignRole('agent');

    // Create property types and subtypes
    $this->propertyType = PropertyType::create(['name' => 'House']);
    $this->propertySubType = PropertySubType::create([
        'name' => 'Single Family',
        'property_type_id' => $this->propertyType->id,
    ]);

    // Create properties for the agent
    $this->userProperty1 = Property::factory()->create([
        'title' => 'My First Property',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);

    $this->userProperty2 = Property::factory()->create([
        'title' => 'My Second Property',
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'draft',
    ]);

    // Create property for other agent
    $this->otherProperty = Property::factory()->create([
        'title' => 'Other Agent Property',
        'user_id' => $this->otherAgent->id,
        'property_sub_type_id' => $this->propertySubType->id,
        'status' => 'published',
    ]);
});

test('my properties component renders correctly for authenticated user', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertStatus(200)
        ->assertViewIs('livewire.my-properties')
        ->assertSee('My First Property')
        ->assertSee('My Second Property')
        ->assertDontSee('Other Agent Property');
});

test('my properties shows only user properties', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee($this->userProperty1->title)
        ->assertSee($this->userProperty2->title)
        ->assertDontSee($this->otherProperty->title);
});

test('delete property removes property successfully', function (): void {
    $this->actingAs($this->agent);

    $propertyId = $this->userProperty1->id;

    Livewire::test(MyProperties::class)
        ->call('deleteProperty', $this->userProperty1);

    // Verify property is deleted from database
    expect(Property::find($propertyId))->toBeNull();
});

test('delete property fails for unauthorized user', function (): void {
    $this->actingAs($this->otherAgent);

    // Test that the property still exists after unauthorized attempt
    $propertyId = $this->userProperty1->id;

    try {
        Livewire::test(MyProperties::class)
            ->call('deleteProperty', $this->userProperty1);
    } catch (Exception $e) {
        // Expected to fail
    }

    // Verify property still exists
    expect(Property::find($propertyId))->not->toBeNull();
});

test('update property status works correctly', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->call('updatePropertyStatus', $this->userProperty2, 'published');

    // Verify status is updated in database
    $this->userProperty2->refresh();
    expect($this->userProperty2->status->value)->toBe('published');
});

test('update property status fails for unauthorized user', function (): void {
    $this->actingAs($this->otherAgent);

    $originalStatus = $this->userProperty1->status->value;

    try {
        Livewire::test(MyProperties::class)
            ->call('updatePropertyStatus', $this->userProperty1, 'sold');
    } catch (Exception $e) {
        // Expected to fail
    }

    // Verify status hasn't changed
    $this->userProperty1->refresh();
    expect($this->userProperty1->status->value)->toBe($originalStatus);
});

test('component shows empty state when no properties', function (): void {
    $userWithoutProperties = User::factory()->create();
    $userWithoutProperties->assignRole('agent');

    $this->actingAs($userWithoutProperties);

    Livewire::test(MyProperties::class)
        ->assertSee("You haven't listed any properties yet")
        ->assertSee('List your first property!');
});

test('component displays property status correctly', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee('Published') // For userProperty1
        ->assertSee('Draft'); // For userProperty2
});

test('component shows edit and delete buttons', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee('Edit')
        ->assertSee('Delete');
});

test('component shows appropriate action buttons based on status', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee('Mark Sold') // For published property
        ->assertSee('Publish'); // For draft property
});

test('pagination works correctly', function (): void {
    $this->actingAs($this->agent);

    // Create more properties to trigger pagination
    Property::factory(15)->create([
        'user_id' => $this->agent->id,
        'property_sub_type_id' => $this->propertySubType->id,
    ]);

    Livewire::test(MyProperties::class)
        ->assertSee('My Property Listings')
        ->assertSee($this->userProperty1->title);
});

test('property images display correctly with media library', function (): void {
    $this->actingAs($this->agent);

    // Create a test image file
    $testImagePath = storage_path('app/test-image.jpg');
    file_put_contents($testImagePath, base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='));

    // Add media to property
    $this->userProperty1->addMedia($testImagePath)
        ->toMediaCollection('gallery');

    Livewire::test(MyProperties::class)
        ->assertSee($this->userProperty1->getFirstMediaUrl('gallery', 'preview'));

    // Clean up
    if (file_exists($testImagePath)) {
        unlink($testImagePath);
    }
});

test('component handles properties without images', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee('https://via.placeholder.com/400x300?text=No+Image');
});

test('component uses property repository correctly', function (): void {
    $this->actingAs($this->agent);

    $component = Livewire::test(MyProperties::class);

    // Verify that the component renders correctly (indicating repository is working)
    $component->assertStatus(200);
    $component->assertSee('My Property Listings');
});

test('component displays property details correctly', function (): void {
    $this->actingAs($this->agent);

    Livewire::test(MyProperties::class)
        ->assertSee($this->userProperty1->title)
        ->assertSee($this->userProperty1->address_line_1)
        ->assertSee($this->userProperty1->city)
        ->assertSee('$'.number_format($this->userProperty1->price));
});
