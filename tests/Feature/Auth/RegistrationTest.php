<?php

declare(strict_types=1);

use Livewire\Volt\Volt;
use Spatie\Permission\Models\Role;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function (): void {
    // Create roles for registration
    Role::create(['name' => 'seeker']);
    Role::create(['name' => 'agent']);
});

test('registration screen can be rendered', function (): void {
    $response = $this->get('/register');

    $response->assertStatus(200);
});

test('new users can register', function (): void {
    Volt::test('auth.register')
        ->set('name', 'Test User')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->call('register')
        ->assertRedirect(route('dashboard', absolute: false));

    $this->assertAuthenticated();
});
