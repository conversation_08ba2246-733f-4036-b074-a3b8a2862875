<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

test('user can upload profile photo', function (): void {
    Storage::fake('public');

    $user = User::factory()->create();
    $this->actingAs($user);

    $file = UploadedFile::fake()->image('profile.jpg', 100, 100);

    Livewire::test(App\Livewire\Profile\UpdateProfileInformation::class)
        ->set('profile_photo', $file)
        ->call('updateProfileInformation');

    expect($user->fresh()->getFirstMediaUrl('profile_photo'))->not->toBeEmpty();
});

test('profile photo validation rejects invalid files', function (): void {
    $user = User::factory()->create();
    $this->actingAs($user);

    $file = UploadedFile::fake()->create('document.pdf', 100);

    Livewire::test(App\Livewire\Profile\UpdateProfileInformation::class)
        ->set('profile_photo', $file)
        ->call('updateProfileInformation')
        ->assertHasErrors(['profile_photo']);
});

test('profile photo validation rejects oversized files', function (): void {
    $user = User::factory()->create();
    $this->actingAs($user);

    $file = UploadedFile::fake()->image('large.jpg')->size(2048); // 2MB

    Livewire::test(App\Livewire\Profile\UpdateProfileInformation::class)
        ->set('profile_photo', $file)
        ->call('updateProfileInformation')
        ->assertHasErrors(['profile_photo']);
});
