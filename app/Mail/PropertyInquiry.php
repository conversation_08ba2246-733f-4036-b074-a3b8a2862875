<?php

declare(strict_types=1);

namespace App\Mail;

use App\Models\Property;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

// Added for Lokus MVP

final class PropertyInquiry extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(public Property $property, public string $inquirerName, public string $inquirerEmail, public ?string $inquirerPhone, public string $inquiryMessage) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Inquiry for Your Property: '.$this->property->title,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.property-inquiry',
            with: [
                'listerName' => $this->property->user->name,
                'propertyTitle' => $this->property->title,
                'propertyId' => $this->property->id,
                'inquirerName' => $this->inquirerName,
                'inquirerEmail' => $this->inquirerEmail,
                'inquiryMessage' => $this->inquiryMessage,
                'propertyUrl' => route('properties.show', $this->property->id),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
