<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Property;
use App\Models\User;

final class PropertyPolicy
{
    /**
     * Determine whether the user can view the property.
     */
    public function view(?User $user, Property $property): bool
    {
        // Published properties can be viewed by anyone
        if ($property->status->value === 'published') {
            return true;
        }

        // Draft properties can only be viewed by owner or admin
        return $user && ($user->id === $property->user_id || $user->hasRole('admin'));
    }

    /**
     * Determine whether the user can update the property.
     */
    public function update(User $user, Property $property): bool
    {
        return $user->id === $property->user_id || $user->hasRole('admin');
    }

    /**
     * Determine whether the user can delete the property.
     */
    public function delete(User $user, Property $property): bool
    {
        return $user->id === $property->user_id || $user->hasRole('admin');
    }

    /**
     * Determine whether the user can feature the property.
     */
    public function feature(User $user): bool
    {
        return $user->hasRole('admin');
    }
}
