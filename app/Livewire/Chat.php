<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Message;
use App\Models\Property;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\Component;

final class Chat extends Component
{
    public $messages = [];

    public $newMessage = '';

    public $receiverId;

    public $propertyId;

    public function mount($receiverId, $propertyId = null): void
    {
        $this->receiverId = $receiverId;
        $this->propertyId = $propertyId;
        $this->loadMessages();
    }

    public function loadMessages(): void
    {
        $userId = Auth::id();
        $this->messages = Message::where(function ($query) use ($userId): void {
            $query->where('sender_id', $userId)
                ->where('receiver_id', $this->receiverId);
        })->orWhere(function ($query) use ($userId): void {
            $query->where('sender_id', $this->receiverId)
                ->where('receiver_id', $userId);
        })
            ->when($this->propertyId, function ($query): void {
                $query->where('property_id', $this->propertyId);
            })
            ->with(['sender', 'receiver'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    public function sendMessage(): void
    {
        $this->validate();

        Message::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $this->receiverId,
            'property_id' => $this->propertyId,
            'content' => $this->newMessage,
        ]);

        $this->newMessage = '';
        $this->loadMessages();

        // Emit event for real-time update
        $this->dispatch('messageSent');
    }

    #[On('echo-private:chat.{receiverId},MessageSent')]
    public function handleMessageSent($event): void
    {
        $this->loadMessages();
    }

    public function render()
    {
        $receiver = User::find($this->receiverId);
        $property = $this->propertyId ? Property::find($this->propertyId) : null;

        return view('livewire.chat', [
            'receiver' => $receiver,
            'property' => $property,
        ]);
    }
}
