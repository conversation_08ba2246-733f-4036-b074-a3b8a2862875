<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Enums\PropertyStatus;
use App\Models\Property;
use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

final class MyProperties extends Component
{
    use WithPagination;

    private PropertyRepository $propertyRepository;

    public function boot(PropertyRepository $propertyRepository): void
    {
        $this->propertyRepository = $propertyRepository;
    }

    public function deleteProperty(Property $property): void
    {
        $this->authorize('delete', $property);

        // Media files are automatically deleted by Spatie Media Library when the model is deleted
        $property->delete();
        session()->flash('message', 'Property deleted successfully!');
    }

    public function updatePropertyStatus(Property $property, $status): void
    {
        $this->authorize('update', $property);

        $property->status = $status;
        $property->save();

        // Dispatch PropertyIsLive event if property is being published
        if ($status === PropertyStatus::Published->value) {
            event(new \App\Events\PropertyIsLive($property));
        }

        session()->flash('message', 'Property status updated successfully!');
    }

    public function render()
    {
        $properties = $this->propertyRepository->getUserProperties(Auth::id());

        return view('livewire.my-properties', [
            'properties' => $properties,
        ]);
    }
}
