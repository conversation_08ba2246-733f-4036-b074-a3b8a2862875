<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

final class FavoriteButton extends Component
{
    public $property;

    public $isFavorited;

    public function mount(Property $property): void
    {
        $this->property = $property;
        $this->checkIfFavorited();
    }

    public function checkIfFavorited(): void
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            $this->isFavorited = $user->favoritedProperties()->where('property_id', $this->property->id)->exists();
        } else {
            $this->isFavorited = false;
        }
    }

    public function toggleFavorite()
    {
        if (! Auth::check()) {
            return redirect()->route('login');
        }

        /** @var \App\Models\User $user */
        $user = Auth::user();

        if ($this->isFavorited) {
            $user->favoritedProperties()->detach($this->property->id);
        } else {
            $user->favoritedProperties()->attach($this->property->id);
        }

        $this->checkIfFavorited();

        return null;
    }

    public function render()
    {
        return view('livewire.favorite-button');
    }
}
