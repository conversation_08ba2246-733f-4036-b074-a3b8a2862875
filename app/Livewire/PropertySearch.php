<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\PropertyRepository;
use Livewire\Component;
use Livewire\WithPagination;

final class PropertySearch extends Component
{
    use WithPagination;

    public $keywords = '';

    public $property_type_id = '';

    public $property_sub_type_id = '';

    public $listing_type = '';

    public $min_price = '';

    public $max_price = '';

    public $sort_by = 'created_at';

    public $sort_direction = 'desc';

    public $total_properties;

    private PropertyRepository $propertyRepository;

    private array $queryString = [
        'keywords' => ['except' => ''],
        'property_type_id' => ['except' => ''],
        'property_sub_type_id' => ['except' => ''],
        'listing_type' => ['except' => ''],
        'min_price' => ['except' => ''],
        'max_price' => ['except' => ''],
        'sort_by' => ['except' => 'created_at'],
        'sort_direction' => ['except' => 'desc'],
    ];

    // Add this property to the class
    private array $propertiesData = [];

    public function boot(PropertyRepository $propertyRepository): void
    {
        $this->propertyRepository = $propertyRepository;
    }

    public function updating($key): void
    {
        if (in_array($key, ['keywords', 'property_type_id', 'property_sub_type_id', 'listing_type', 'min_price', 'max_price', 'sort_by', 'sort_direction'])) {
            $this->resetPage();
        }
    }

    public function updatedPropertyTypeId(): void
    {
        $this->property_sub_type_id = '';
        $this->resetPage();
    }

    public function render()
    {
        // Build search query using repository
        $query = $this->propertyRepository->buildSearchQuery([
            'keywords' => $this->keywords,
            'property_type_id' => $this->property_type_id,
            'property_sub_type_id' => $this->property_sub_type_id,
            'listing_type' => $this->listing_type,
            'min_price' => $this->min_price,
            'max_price' => $this->max_price,
            'sort_by' => $this->sort_by,
            'sort_direction' => $this->sort_direction,
            'apply_user_budget' => true,
        ]);

        // Only show published properties
        $query = $this->propertyRepository->publishedOnly($query);

        // Calculate total properties after filtering
        $this->total_properties = $query->count();

        $properties = $query->paginate(10);

        // Convert to array for JavaScript
        $propertiesData = $this->propertyRepository->toJavaScriptFormat($properties->getCollection());

        // Only dispatch event if properties have changed
        if ($this->propertiesData !== $propertiesData) {
            $this->propertiesData = $propertiesData;
            $this->dispatch('propertiesUpdated', properties: $propertiesData);
        }

        $propertyTypes = $this->propertyRepository->getPropertyTypes();
        $propertySubTypes = $this->propertyRepository->getPropertySubTypes($this->property_type_id);

        return view('livewire.property-search', [
            'properties' => $properties,
            'propertiesData' => $propertiesData,
            'propertyTypes' => $propertyTypes,
            'propertySubTypes' => $propertySubTypes,
        ]);
    }

    public function showProperty($propertyId)
    {
        return redirect()->route('properties.show', $propertyId);
    }

    public function search(): void
    {
        $this->resetPage();
    }

    public function resetFilters(): void
    {
        $this->reset([
            'keywords',
            'property_type_id',
            'property_sub_type_id',
            'listing_type',
            'min_price',
            'max_price',
            'sort_by',
            'sort_direction',
        ]);
        $this->resetPage();
    }
}
