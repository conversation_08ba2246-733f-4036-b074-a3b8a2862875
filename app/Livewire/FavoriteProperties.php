<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

final class FavoriteProperties extends Component
{
    public $properties;

    private PropertyRepository $propertyRepository;

    public function boot(PropertyRepository $propertyRepository): void
    {
        $this->propertyRepository = $propertyRepository;
    }

    public function mount(): void
    {
        $this->loadProperties();
    }

    public function loadProperties(): void
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $this->properties = $this->propertyRepository->getUserFavorites($user);
    }

    public function render()
    {
        return view('livewire.favorite-properties');
    }
}
