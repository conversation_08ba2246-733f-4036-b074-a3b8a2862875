<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Mail\PropertyInquiry;
use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Livewire\Attributes\On;
use Livewire\Component;

// Import the On attribute

final class ContactLister extends Component
{
    public ?Property $property = null; // Will be an instance of Property

    public $name = '';

    public $email = '';

    public $phone = '';

    public $message = '';

    public $showModal = false;

    public function mount(): void
    {
        if (Auth::check()) {
            $this->name = Auth::user()->name;
            $this->email = Auth::user()->email;
            $this->phone = Auth::user()->phone;
        }
    }

    #[On('open-contact-modal')]
    public function openModal($propertyId): void
    {
        $this->property = Property::with(['user', 'propertySubType.propertyType'])->findOrFail($propertyId);
        $this->showModal = true;
    }

    public function sendInquiry(): void
    {
        $this->validate();

        Mail::to($this->property->user->email)->send(new PropertyInquiry(
            $this->property,
            $this->name,
            $this->email,
            $this->phone,
            $this->message
        ));

        session()->flash('inquiry_message', 'Your inquiry has been sent successfully!');
        $this->reset(['name', 'email', 'phone', 'message']); // Clear form fields
        $this->showModal = false; // Close the modal
    }

    public function render()
    {
        return view('livewire.contact-lister');
    }
}
