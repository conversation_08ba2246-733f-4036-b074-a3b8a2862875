<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

#[Layout('components.layouts.admin', ['title' => 'User Management'])]
final class UserManagement extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $statusFilter = '';

    public $sortField = 'created_at';

    public $sortDirection = 'desc';

    private array $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key): void
    {
        if (in_array($key, ['search', 'roleFilter', 'statusFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $users = User::query();

        if ($this->search) {
            $users->where(function ($query): void {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%')
                    ->orWhere('phone', 'like', '%'.$this->search.'%');
            });
        }

        if ($this->roleFilter) {
            $users->whereHas('roles', function ($query): void {
                $query->where('name', $this->roleFilter);
            });
        }

        if ($this->statusFilter !== '') {
            $users->where('is_active', (bool) $this->statusFilter);
        }

        $users->orderBy($this->sortField, $this->sortDirection);

        return view('livewire.admin.user-management', [
            'users' => $users->paginate(10),
            'roles' => Role::all(),
        ]);
    }

    public function toggleActive($userId): void
    {
        $user = User::findOrFail($userId);
        $user->is_active = ! $user->is_active;
        $user->save();
    }

    public function updateRole($userId, $role): void
    {
        $user = User::findOrFail($userId);
        $user->syncRoles([$role]);
    }

    public function deleteUser($userId): void
    {
        User::destroy($userId);
    }
}
