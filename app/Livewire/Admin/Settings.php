<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('components.layouts.admin', ['title' => 'Admin Settings'])]
final class Settings extends Component
{
    public $activeTab = 'taxonomy';

    public function render()
    {
        return view('livewire.admin.settings');
    }

    public function setActiveTab($tab): void
    {
        $this->activeTab = $tab;
    }
}
