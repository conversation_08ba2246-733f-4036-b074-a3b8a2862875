<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Admin Dashboard'])]
final class Dashboard extends Component
{
    public function render()
    {
        // Basic counts
        $totalUsers = User::count();
        $totalProperties = Property::count();
        $publishedProperties = Property::where('status', 'published')->count();
        $pendingProperties = Property::where('status', 'draft')->count();

        // User statistics by role
        $adminUsers = User::role('admin')->count();
        $agentUsers = User::role('agent')->count();
        $seekerUsers = User::role('seeker')->count();
        $activeUsers = User::where('is_active', true)->count();
        $inactiveUsers = User::where('is_active', false)->count();

        // Property statistics by status
        $soldProperties = Property::where('status', 'sold')->count();
        $rentedProperties = Property::where('status', 'rented')->count();
        $underOfferProperties = Property::where('status', 'under_offer')->count();

        // Property types breakdown
        $propertyTypes = PropertyType::withCount('properties')
            ->get()
            ->pluck('properties_count', 'name')
            ->toArray();
        $propertyTypePercentages = [];
        foreach ($propertyTypes as $type => $count) {
            $propertyTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Listing types breakdown
        $listingTypes = Property::selectRaw('listing_type, COUNT(*) as count')
            ->groupBy('listing_type')
            ->pluck('count', 'listing_type')
            ->toArray();
        $listingTypePercentages = [];
        foreach ($listingTypes as $type => $count) {
            $listingTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Recent activity - last 5 properties and users
        $recentProperties = Property::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Average property price
        $averagePrice = Property::where('status', 'published')->avg('price');

        // Properties by city (top 5)
        $topCities = Property::selectRaw('city, COUNT(*) as count')
            ->groupBy('city')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->pluck('count', 'city')
            ->toArray();
        $topCityPercentages = [];
        foreach ($topCities as $city => $count) {
            $topCityPercentages[$city] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Media health statistics
        $totalMedia = Media::count();
        $mediaWithConversions = Media::whereJsonLength('generated_conversions', '>', 0)->count();
        $mediaWithoutConversions = $totalMedia - $mediaWithConversions;
        $conversionSuccessRate = $totalMedia > 0 ? round(($mediaWithConversions / $totalMedia) * 100, 1) : 0;

        // Queue health statistics
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        $mediaJobs = DB::table('jobs')->where('payload', 'like', '%PerformConversionsJob%')->count();

        $mediaHealthStatus = 'healthy';
        if ($conversionSuccessRate < 80) {
            $mediaHealthStatus = 'warning';
        }
        if ($conversionSuccessRate < 50 || $failedJobs > 10) {
            $mediaHealthStatus = 'critical';
        }

        return view('livewire.admin.dashboard', ['totalUsers' => $totalUsers, 'totalProperties' => $totalProperties, 'publishedProperties' => $publishedProperties, 'pendingProperties' => $pendingProperties, 'adminUsers' => $adminUsers, 'agentUsers' => $agentUsers, 'seekerUsers' => $seekerUsers, 'activeUsers' => $activeUsers, 'inactiveUsers' => $inactiveUsers, 'soldProperties' => $soldProperties, 'rentedProperties' => $rentedProperties, 'underOfferProperties' => $underOfferProperties, 'propertyTypes' => $propertyTypes, 'propertyTypePercentages' => $propertyTypePercentages, 'listingTypes' => $listingTypes, 'listingTypePercentages' => $listingTypePercentages, 'topCities' => $topCities, 'topCityPercentages' => $topCityPercentages, 'averagePrice' => $averagePrice, 'recentProperties' => $recentProperties, 'recentUsers' => $recentUsers, 'totalMedia' => $totalMedia, 'mediaWithConversions' => $mediaWithConversions, 'mediaWithoutConversions' => $mediaWithoutConversions, 'conversionSuccessRate' => $conversionSuccessRate, 'pendingJobs' => $pendingJobs, 'failedJobs' => $failedJobs, 'mediaJobs' => $mediaJobs, 'mediaHealthStatus' => $mediaHealthStatus]);
    }
}
