<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;

#[Layout('components.layouts.admin', ['title' => 'User Reports'])]
final class UserReports extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $statusFilter = '';

    public $sortField = 'created_at';

    public $sortDirection = 'desc';

    private array $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key): void
    {
        if (in_array($key, ['search', 'roleFilter', 'statusFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $users = User::query();

        if ($this->search) {
            $users->where(function ($query): void {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%')
                    ->orWhere('phone', 'like', '%'.$this->search.'%');
            });
        }

        if ($this->roleFilter) {
            $users->whereHas('roles', function ($query): void {
                $query->where('name', $this->roleFilter);
            });
        }

        if ($this->statusFilter !== '') {
            $users->where('is_active', (bool) $this->statusFilter);
        }

        $users->orderBy($this->sortField, $this->sortDirection);

        // Statistics
        $totalUsers = User::count();
        $activeUsers = User::where('is_active', true)->count();
        $adminUsers = User::role('admin')->count();
        $agentUsers = User::role('agent')->count();
        $seekerUsers = User::whereDoesntHave('roles', function ($query): void {
            $query->whereIn('name', ['admin', 'agent']);
        })->count();

        return view('livewire.admin.user-reports', [
            'users' => $users->paginate(10),
            'totalUsers' => $totalUsers,
            'activeUsers' => $activeUsers,
            'adminUsers' => $adminUsers,
            'agentUsers' => $agentUsers,
            'seekerUsers' => $seekerUsers,
        ]);
    }
}
