<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Amenity;
use Livewire\Component;
use Livewire\WithPagination;

final class AmenityManager extends Component
{
    use WithPagination;

    public $name = '';

    public $amenity_id;

    public $is_editing = false;

    private array $rules = [
        'name' => 'required|string|max:255|unique:amenities,name',
    ];

    public function render()
    {
        return view('livewire.admin.amenity-manager', [
            'amenities' => Amenity::paginate(10),
        ]);
    }

    public function saveAmenity(): void
    {
        $this->validate();

        Amenity::create(['name' => $this->name]);

        $this->reset(['name']);
        session()->flash('message', 'Amenity created successfully.');
    }

    public function edit($id): void
    {
        $amenity = Amenity::findOrFail($id);
        $this->amenity_id = $id;
        $this->name = $amenity->name;
        $this->is_editing = true;
    }

    public function updateAmenity(): void
    {
        $this->validate([
            'name' => 'required|string|max:255|unique:amenities,name,'.$this->amenity_id,
        ]);

        $amenity = Amenity::findOrFail($this->amenity_id);
        $amenity->update(['name' => $this->name]);

        $this->reset(['name', 'amenity_id', 'is_editing']);
        session()->flash('message', 'Amenity updated successfully.');
    }

    public function delete($id): void
    {
        Amenity::findOrFail($id)->delete();
        session()->flash('message', 'Amenity deleted successfully.');
    }
}
