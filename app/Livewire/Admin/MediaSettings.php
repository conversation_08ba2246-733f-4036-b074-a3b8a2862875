<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Property;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Media Management'])]
final class MediaSettings extends Component
{
    use WithPagination;
    public $activeTab = 'conversions';

    public $isRegenerating = false;

    public $regenerationProgress = 0;

    public $regenerationTotal = 0;

    public $regenerationCurrent = 0;

    public $selectedProperties = [];

    public $selectAll = false;

    public $showBulkActions = false;

    public $operationMessage = '';

    public $operationType = 'info'; // info, success, error, warning

    protected $listeners = [
        'refreshMediaStats' => '$refresh',
        'regenerationComplete' => 'handleRegenerationComplete',
    ];

    public function mount(): void
    {
        // Authorization is handled by the route middleware (role:admin)
        // No additional gate check needed
    }

    public function render()
    {
        $mediaStats = $this->getMediaStatistics();
        $queueStats = $this->getQueueStatistics();
        $properties = $this->getPropertiesWithMedia();

        return view('livewire.admin.media-settings', ['mediaStats' => $mediaStats, 'queueStats' => $queueStats, 'properties' => $properties]);
    }

    public function setActiveTab($tab): void
    {
        $this->activeTab = $tab;
        $this->resetOperationMessage();

        // Reset pagination when switching tabs
        $this->resetPage();

        // Reset selection state
        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function regenerateAllConversions(): void
    {
        if ($this->isRegenerating) {
            return;
        }

        $this->isRegenerating = true;
        $this->regenerationProgress = 0;
        $this->regenerationTotal = Media::count();
        $this->regenerationCurrent = 0;

        Log::info('Admin initiated media conversion regeneration', [
            'user_id' => Auth::id(),
            'total_media' => $this->regenerationTotal,
        ]);

        try {
            // Use Artisan command to regenerate conversions
            Artisan::call('media-library:regenerate', [
                '--force' => true,
            ]);

            $this->setOperationMessage('All media conversions have been queued for regeneration.', 'success');

        } catch (Exception $e) {
            Log::error('Failed to regenerate media conversions', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
        }

        $this->isRegenerating = false;
    }

    public function regenerateSelectedConversions(): void
    {
        if (empty($this->selectedProperties)) {
            $this->setOperationMessage('Please select properties to regenerate conversions for.', 'warning');

            return;
        }

        $properties = Property::whereIn('id', $this->selectedProperties)->with('media')->get();
        $mediaIds = [];
        $mediaCount = 0;

        foreach ($properties as $property) {
            foreach ($property->media as $media) {
                $mediaIds[] = $media->id;
                $mediaCount++;
            }
        }

        if ($mediaIds !== []) {
            try {
                // Use Artisan command to regenerate conversions for specific media IDs
                Artisan::call('media-library:regenerate', [
                    '--ids' => implode(',', $mediaIds),
                    '--force' => true,
                ]);

                Log::info('Admin initiated selective media conversion regeneration', [
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                    'media_count' => $mediaCount,
                    'media_ids' => $mediaIds,
                ]);

                $this->setOperationMessage("Queued regeneration for {$mediaCount} media files from ".count($this->selectedProperties).' properties.', 'success');
            } catch (Exception $e) {
                Log::error('Failed to regenerate selected media conversions', [
                    'error' => $e->getMessage(),
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                ]);

                $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
            }
        } else {
            $this->setOperationMessage('No media found for selected properties.', 'warning');
        }

        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function clearConversionCache(): void
    {
        try {
            // Clear Laravel cache
            Artisan::call('cache:clear');

            // Clear media conversion cache if exists
            if (Storage::disk('public')->exists('conversions')) {
                Storage::disk('public')->deleteDirectory('conversions');
            }

            Log::info('Admin cleared media conversion cache', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Media conversion cache cleared successfully.', 'success');

        } catch (Exception $e) {
            Log::error('Failed to clear media conversion cache', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to clear cache: '.$e->getMessage(), 'error');
        }
    }

    public function processQueueJobs(): void
    {
        try {
            // Process queue jobs
            Artisan::call('queue:work', [
                '--once' => true,
                '--timeout' => 60,
            ]);

            $this->setOperationMessage('Queue jobs processed successfully.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to process queue jobs: '.$e->getMessage(), 'error');
        }
    }

    public function retryFailedJobs(): void
    {
        try {
            Artisan::call('queue:retry', ['--all' => true]);

            Log::info('Admin retried all failed queue jobs', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('All failed jobs have been retried.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to retry jobs: '.$e->getMessage(), 'error');
        }
    }

    public function clearFailedJobs(): void
    {
        try {
            Artisan::call('queue:flush');

            Log::info('Admin cleared all failed queue jobs', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('All failed jobs have been cleared.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to clear failed jobs: '.$e->getMessage(), 'error');
        }
    }

    public function toggleSelectAll(): void
    {
        if ($this->selectAll) {
            // Get current page properties only
            $currentPageProperties = $this->getPropertiesWithMedia();
            $this->selectedProperties = $currentPageProperties->pluck('id')->toArray();
        } else {
            $this->selectedProperties = [];
        }

        $this->showBulkActions = ! empty($this->selectedProperties);
    }

    public function updatedSelectedProperties(): void
    {
        $this->showBulkActions = ! empty($this->selectedProperties);

        // Get current page properties for comparison
        $currentPageProperties = $this->getPropertiesWithMedia();
        $currentPageIds = $currentPageProperties->pluck('id')->toArray();

        // Check if all current page items are selected
        $this->selectAll = count($currentPageIds) > 0 &&
                          count(array_intersect($this->selectedProperties, $currentPageIds)) === count($currentPageIds);
    }

    public function handleRegenerationComplete(): void
    {
        $this->isRegenerating = false;
        $this->regenerationProgress = 100;
        $this->setOperationMessage('Media conversion regeneration completed successfully.', 'success');
    }

    private function getMediaStatistics(): array
    {
        try {
            $totalMedia = Media::count();

            // Get conversion statistics using proper JSON queries
            $conversionStats = Media::selectRaw('
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true THEN 1 ELSE 0 END) as thumb_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as preview_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true AND JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as complete_conversions
            ')->first();

            $thumbCount = (int) ($conversionStats->thumb_count ?? 0);
            $previewCount = (int) ($conversionStats->preview_count ?? 0);
            $completeConversions = (int) ($conversionStats->complete_conversions ?? 0);

            // Calculate media with at least one conversion
            $mediaWithConversions = Media::whereRaw('
                JSON_EXTRACT(generated_conversions, "$.thumb") = true OR
                JSON_EXTRACT(generated_conversions, "$.preview") = true
            ')->count();

            $mediaWithoutConversions = $totalMedia - $mediaWithConversions;

            // Calculate success rate based on complete conversions (both thumb and preview)
            $conversionSuccessRate = $totalMedia > 0 ? round(($completeConversions / $totalMedia) * 100, 1) : 0;

            $storageSize = $this->calculateStorageSize();
        } catch (Exception $e) {
            Log::error('Failed to get media statistics', ['error' => $e->getMessage()]);

            // Return safe defaults
            return [
                'total_media' => 0,
                'with_conversions' => 0,
                'without_conversions' => 0,
                'complete_conversions' => 0,
                'thumb_conversions' => 0,
                'preview_conversions' => 0,
                'storage_size' => 'Unable to calculate',
                'conversion_success_rate' => 0,
                'thumb_success_rate' => 0,
                'preview_success_rate' => 0,
            ];
        }

        return [
            'total_media' => $totalMedia,
            'with_conversions' => $mediaWithConversions,
            'without_conversions' => $mediaWithoutConversions,
            'complete_conversions' => $completeConversions,
            'thumb_conversions' => $thumbCount,
            'preview_conversions' => $previewCount,
            'storage_size' => $storageSize,
            'conversion_success_rate' => $conversionSuccessRate,
            'thumb_success_rate' => $totalMedia > 0 ? round(($thumbCount / $totalMedia) * 100, 1) : 0,
            'preview_success_rate' => $totalMedia > 0 ? round(($previewCount / $totalMedia) * 100, 1) : 0,
        ];
    }

    private function getQueueStatistics(): array
    {
        try {
            $pendingJobs = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            // Get media conversion specific jobs with more detail
            $mediaJobs = DB::table('jobs')
                ->where('payload', 'like', '%PerformConversionsJob%')
                ->count();

            // Get failed media conversion jobs
            $failedMediaJobs = DB::table('failed_jobs')
                ->where('payload', 'like', '%PerformConversionsJob%')
                ->count();

            // Calculate processing statistics
            $totalProcessedJobs = $mediaJobs + $failedMediaJobs;
            $mediaJobSuccessRate = $totalProcessedJobs > 0 ?
                round((($totalProcessedJobs - $failedMediaJobs) / $totalProcessedJobs) * 100, 1) : 100;

            return [
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'media_jobs' => $mediaJobs,
                'failed_media_jobs' => $failedMediaJobs,
                'media_job_success_rate' => $mediaJobSuccessRate,
                'queue_status' => $this->getQueueWorkerStatus(),
                'last_failed_job' => $this->getLastFailedJob(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to get queue statistics', ['error' => $e->getMessage()]);

            // Return safe defaults
            return [
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'media_jobs' => 0,
                'failed_media_jobs' => 0,
                'media_job_success_rate' => 100,
                'queue_status' => 'unknown',
                'last_failed_job' => null,
            ];
        }
    }

    private function getQueueWorkerStatus(): string
    {
        // Simple check - in production you might want to use Horizon or Supervisor status
        $processes = shell_exec('ps aux | grep "queue:work" | grep -v grep | wc -l');

        return (int) $processes > 0 ? 'running' : 'stopped';
    }

    private function getLastFailedJob(): ?array
    {
        $lastFailedJob = DB::table('failed_jobs')
            ->where('payload', 'like', '%PerformConversionsJob%')
            ->orderBy('failed_at', 'desc')
            ->first();

        if (!$lastFailedJob) {
            return null;
        }

        return [
            'failed_at' => $lastFailedJob->failed_at,
            'exception' => substr($lastFailedJob->exception, 0, 200) . '...', // Truncate for display
        ];
    }

    private function calculateStorageSize(): string
    {
        $totalSize = 0;

        try {
            // Get total size from database (more efficient than loading all models)
            $mediaSizes = Media::selectRaw('SUM(size) as total_size')->first();
            $totalSize = (int) ($mediaSizes->total_size ?? 0);

            // For conversion sizes, estimate based on conversion count
            // This avoids potential file system issues while still providing useful data
            $conversionStats = Media::selectRaw('
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true THEN 1 ELSE 0 END) as thumb_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as preview_count
            ')->first();

            $thumbCount = (int) ($conversionStats->thumb_count ?? 0);
            $previewCount = (int) ($conversionStats->preview_count ?? 0);

            // Estimate conversion sizes (thumb ~10KB, preview ~50KB average)
            $estimatedConversionSize = ($thumbCount * 10240) + ($previewCount * 51200);
            $totalSize += $estimatedConversionSize;

        } catch (Exception $e) {
            // If database queries fail, return a safe default
            Log::error('Failed to calculate storage size', ['error' => $e->getMessage()]);
            return 'Unable to calculate';
        }

        return $this->formatBytes($totalSize);
    }

    private function formatBytes(int $bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision).' '.$units[$i];
    }

    private function getPropertiesWithMedia()
    {
        return Property::with(['media', 'user'])
            ->whereHas('media')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);
    }

    private function setOperationMessage(string $message, string $type = 'info'): void
    {
        $this->operationMessage = $message;
        $this->operationType = $type;
    }

    private function resetOperationMessage(): void
    {
        $this->operationMessage = '';
        $this->operationType = 'info';
    }
}
