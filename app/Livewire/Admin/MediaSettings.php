<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Property;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Media Management'])]
final class MediaSettings extends Component
{
    public $activeTab = 'conversions';

    public $isRegenerating = false;

    public $regenerationProgress = 0;

    public $regenerationTotal = 0;

    public $regenerationCurrent = 0;

    public $selectedProperties = [];

    public $selectAll = false;

    public $showBulkActions = false;

    public $operationMessage = '';

    public $operationType = 'info'; // info, success, error, warning

    protected $listeners = [
        'refreshMediaStats' => '$refresh',
        'regenerationComplete' => 'handleRegenerationComplete',
    ];

    public function mount(): void
    {
        // Authorization is handled by the route middleware (role:admin)
        // No additional gate check needed
    }

    public function render()
    {
        $mediaStats = $this->getMediaStatistics();
        $queueStats = $this->getQueueStatistics();
        $properties = $this->getPropertiesWithMedia();

        return view('livewire.admin.media-settings', ['mediaStats' => $mediaStats, 'queueStats' => $queueStats, 'properties' => $properties]);
    }

    public function setActiveTab($tab): void
    {
        $this->activeTab = $tab;
        $this->resetOperationMessage();
    }

    public function regenerateAllConversions(): void
    {
        if ($this->isRegenerating) {
            return;
        }

        $this->isRegenerating = true;
        $this->regenerationProgress = 0;
        $this->regenerationTotal = Media::count();
        $this->regenerationCurrent = 0;

        Log::info('Admin initiated media conversion regeneration', [
            'user_id' => Auth::id(),
            'total_media' => $this->regenerationTotal,
        ]);

        try {
            // Use Artisan command to regenerate conversions
            Artisan::call('media-library:regenerate', [
                '--force' => true,
            ]);

            $this->setOperationMessage('All media conversions have been queued for regeneration.', 'success');

        } catch (Exception $e) {
            Log::error('Failed to regenerate media conversions', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
        }

        $this->isRegenerating = false;
    }

    public function regenerateSelectedConversions(): void
    {
        if (empty($this->selectedProperties)) {
            $this->setOperationMessage('Please select properties to regenerate conversions for.', 'warning');

            return;
        }

        $properties = Property::whereIn('id', $this->selectedProperties)->with('media')->get();
        $mediaIds = [];
        $mediaCount = 0;

        foreach ($properties as $property) {
            foreach ($property->media as $media) {
                $mediaIds[] = $media->id;
                $mediaCount++;
            }
        }

        if ($mediaIds !== []) {
            try {
                // Use Artisan command to regenerate conversions for specific media IDs
                Artisan::call('media-library:regenerate', [
                    '--ids' => implode(',', $mediaIds),
                    '--force' => true,
                ]);

                Log::info('Admin initiated selective media conversion regeneration', [
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                    'media_count' => $mediaCount,
                    'media_ids' => $mediaIds,
                ]);

                $this->setOperationMessage("Queued regeneration for {$mediaCount} media files from ".count($this->selectedProperties).' properties.', 'success');
            } catch (Exception $e) {
                Log::error('Failed to regenerate selected media conversions', [
                    'error' => $e->getMessage(),
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                ]);

                $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
            }
        } else {
            $this->setOperationMessage('No media found for selected properties.', 'warning');
        }

        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function clearConversionCache(): void
    {
        try {
            // Clear Laravel cache
            Artisan::call('cache:clear');

            // Clear media conversion cache if exists
            if (Storage::disk('public')->exists('conversions')) {
                Storage::disk('public')->deleteDirectory('conversions');
            }

            Log::info('Admin cleared media conversion cache', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Media conversion cache cleared successfully.', 'success');

        } catch (Exception $e) {
            Log::error('Failed to clear media conversion cache', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to clear cache: '.$e->getMessage(), 'error');
        }
    }

    public function processQueueJobs(): void
    {
        try {
            // Process queue jobs
            Artisan::call('queue:work', [
                '--once' => true,
                '--timeout' => 60,
            ]);

            $this->setOperationMessage('Queue jobs processed successfully.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to process queue jobs: '.$e->getMessage(), 'error');
        }
    }

    public function retryFailedJobs(): void
    {
        try {
            Artisan::call('queue:retry', ['--all' => true]);

            Log::info('Admin retried all failed queue jobs', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('All failed jobs have been retried.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to retry jobs: '.$e->getMessage(), 'error');
        }
    }

    public function clearFailedJobs(): void
    {
        try {
            Artisan::call('queue:flush');

            Log::info('Admin cleared all failed queue jobs', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('All failed jobs have been cleared.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to clear failed jobs: '.$e->getMessage(), 'error');
        }
    }

    public function toggleSelectAll(): void
    {
        $this->selectedProperties = $this->selectAll ? $this->getPropertiesWithMedia()->pluck('id')->toArray() : [];

        $this->showBulkActions = ! empty($this->selectedProperties);
    }

    public function updatedSelectedProperties(): void
    {
        $this->showBulkActions = ! empty($this->selectedProperties);
        $totalProperties = $this->getPropertiesWithMedia()->count();
        $this->selectAll = count($this->selectedProperties) === $totalProperties;
    }

    public function handleRegenerationComplete(): void
    {
        $this->isRegenerating = false;
        $this->regenerationProgress = 100;
        $this->setOperationMessage('Media conversion regeneration completed successfully.', 'success');
    }

    private function getMediaStatistics(): array
    {
        $totalMedia = Media::count();
        $mediaWithConversions = Media::whereJsonLength('generated_conversions', '>', 0)->count();
        $mediaWithoutConversions = $totalMedia - $mediaWithConversions;

        $conversionStats = Media::selectRaw('
            SUM(JSON_EXTRACT(generated_conversions, "$.thumb") = true) as thumb_count,
            SUM(JSON_EXTRACT(generated_conversions, "$.preview") = true) as preview_count
        ')->first();

        $storageSize = $this->calculateStorageSize();

        return [
            'total_media' => $totalMedia,
            'with_conversions' => $mediaWithConversions,
            'without_conversions' => $mediaWithoutConversions,
            'thumb_conversions' => $conversionStats->thumb_count ?? 0,
            'preview_conversions' => $conversionStats->preview_count ?? 0,
            'storage_size' => $storageSize,
            'conversion_success_rate' => $totalMedia > 0 ? round(($mediaWithConversions / $totalMedia) * 100, 1) : 0,
        ];
    }

    private function getQueueStatistics(): array
    {
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();

        // Get media conversion specific jobs
        $mediaJobs = DB::table('jobs')
            ->where('payload', 'like', '%PerformConversionsJob%')
            ->count();

        return [
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
            'media_jobs' => $mediaJobs,
            'queue_status' => $this->getQueueWorkerStatus(),
        ];
    }

    private function getQueueWorkerStatus(): string
    {
        // Simple check - in production you might want to use Horizon or Supervisor status
        $processes = shell_exec('ps aux | grep "queue:work" | grep -v grep | wc -l');

        return (int) $processes > 0 ? 'running' : 'stopped';
    }

    private function calculateStorageSize(): string
    {
        $totalSize = 0;
        $mediaFiles = Media::all();

        foreach ($mediaFiles as $media) {
            $path = $media->getPath();
            if (file_exists($path)) {
                $totalSize += filesize($path);
            }

            // Add conversion file sizes
            foreach (['thumb', 'preview'] as $conversion) {
                if ($media->hasGeneratedConversion($conversion)) {
                    $conversionPath = $media->getPath($conversion);
                    if (file_exists($conversionPath)) {
                        $totalSize += filesize($conversionPath);
                    }
                }
            }
        }

        return $this->formatBytes($totalSize);
    }

    private function formatBytes(int $bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision).' '.$units[$i];
    }

    private function getPropertiesWithMedia()
    {
        return Property::with(['media', 'user'])
            ->whereHas('media')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);
    }

    private function setOperationMessage(string $message, string $type = 'info'): void
    {
        $this->operationMessage = $message;
        $this->operationType = $type;
    }

    private function resetOperationMessage(): void
    {
        $this->operationMessage = '';
        $this->operationType = 'info';
    }
}
