<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\PropertyStatus;
use App\Models\Property;
use App\Models\PropertyType;
use Illuminate\Support\Facades\DB;

final class HomeController extends Controller
{
    public function index()
    {
        // Get property types for the search dropdown
        $propertyTypes = PropertyType::with('propertySubTypes')->get();

        // Get popular cities
        $popularCities = Property::select('city', DB::raw('count(*) as count'))
            ->where('status', PropertyStatus::Published)
            ->groupBy('city')
            ->orderBy('count', 'desc')
            ->take(5)
            ->get();

        // Get featured properties
        $featuredProperties = Property::where('status', PropertyStatus::Published)
            ->when(Property::where('is_featured', true)->exists(), fn ($query) => $query->where('is_featured', true), fn ($query) => $query)
            ->with(['user', 'propertySubType.propertyType', 'media'])
            ->latest()
            ->take(4)
            ->get();

        return view('welcome', ['propertyTypes' => $propertyTypes, 'popularCities' => $popularCities, 'featuredProperties' => $featuredProperties]);
    }
}
