<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\PropertyStatus;
use App\Models\Property;
use App\Services\PropertyRepository;
use Illuminate\Support\Facades\Auth;

final class PropertyController extends Controller
{
    public function __construct(private readonly PropertyRepository $propertyRepository) {}

    /**
     * Display a listing of the properties.
     */
    public function index()
    {
        $properties = $this->propertyRepository->getAllPublishedPropertiesCached();

        return view('properties.index', ['properties' => $properties]);
    }

    /**
     * Display the specified property.
     */
    public function show(Property $property)
    {
        if ($property->status !== PropertyStatus::Published && (! Auth::check() || Auth::id() !== $property->user_id)) {
            abort(404); // Only published properties are publicly viewable, or owner can view drafts
        }

        // Eager load relationships for the property detail view
        $property->load(['user', 'propertySubType.propertyType', 'media', 'amenities']);

        return view('properties.show', ['property' => $property]);
    }
}
