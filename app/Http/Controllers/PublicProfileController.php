<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\View\View;

final class PublicProfileController extends Controller
{
    public function show(User $user): View
    {
        $user->load(['properties' => function ($query): void {
            $query->where('status', 'published');
        }, 'media']);

        return view('public-profile.show', ['user' => $user]);
    }
}
