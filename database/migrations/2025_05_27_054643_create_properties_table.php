<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('property_sub_type_id')->constrained('property_sub_types')->onDelete('cascade');
            $table->string('listing_type'); // For Sale, For Rent
            $table->string('title');
            $table->text('description');
            $table->decimal('price', 15, 2);
            $table->string('currency')->default('USD');
            $table->string('address_line_1');
            $table->string('city');
            $table->string('state_region')->nullable();
            $table->string('zip_code')->nullable();
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->unsignedInteger('square_footage')->nullable();
            $table->decimal('lot_size_acres', 8, 2)->nullable();
            $table->string('zoning_details')->nullable();
            $table->boolean('road_access')->default(false);
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('lister_type')->nullable();
            $table->string('status')->default('draft'); // draft, published, sold, rented, under_offer
            $table->boolean('is_featured')->default(false);
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'status'], 'properties_user_status_index');
            $table->index(['city', 'listing_type'], 'properties_city_listing_type_index');
            $table->index(['property_sub_type_id', 'status'], 'properties_subtype_status_index');
            $table->index('status', 'properties_status_index');
            $table->index('city', 'properties_city_index');
            $table->index('listing_type', 'properties_listing_type_index');
            $table->index('created_at', 'properties_created_at_index');
            $table->index('price', 'properties_price_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
    }
};
