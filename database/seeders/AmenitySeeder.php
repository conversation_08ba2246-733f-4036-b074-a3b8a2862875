<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Amenity;
use Illuminate\Database\Seeder;

final class AmenitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = [
            'Swimming Pool', 'Gym', 'Parking', 'Security', 'Balcony', 'Garden',
            'Air Conditioning', 'Heating', 'Wi-Fi', 'Pet Friendly', 'Furnished',
            'Unfurnished', 'Washer/Dryer', 'Dishwasher', 'Fireplace', 'Elevator',
        ];

        foreach ($amenities as $amenity) {
            Amenity::updateOrCreate(['name' => $amenity]);
        }
    }
}
