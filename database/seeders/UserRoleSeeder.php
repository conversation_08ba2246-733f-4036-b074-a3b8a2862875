<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

final class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'phone' => '******-0101',
        ]);
        $admin->assignRole('admin');

        // Create Agent Users
        $agent1 = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'John Lister',
            'password' => Hash::make('password'),
            'phone' => '******-0102',
        ]);
        $agent1->assignRole('agent');

        $agent2 = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Sarah Property Manager',
            'password' => Hash::make('password'),
            'phone' => '******-0103',
        ]);
        $agent2->assignRole('agent');

        // Create Seeker User
        $seeker = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Jane Seeker',
            'password' => Hash::make('password'),
            'phone' => '******-0104',
            'budget_min' => 100000,
            'budget_max' => 500000,
            'search_radius_km' => 20,
        ]);
        $seeker->assignRole('seeker');

        // Create additional users and assign roles
        User::factory(10)->create()->each(function ($user): void {
            $role = random_int(0, 1) !== 0 ? 'agent' : 'seeker';
            $user->assignRole($role);
            if ($role === 'seeker') {
                $user->update([
                    'budget_min' => random_int(50000, 200000),
                    'budget_max' => random_int(250000, 1000000),
                    'search_radius_km' => random_int(5, 50),
                ]);
            }
        });
    }
}
