<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\PropertySubType;
use App\Models\PropertyType;
use Illuminate\Database\Seeder;

final class PropertySubTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subTypes = [
            'Residential' => ['Bedsitter', 'Single Room', 'Double Room', 'Apartment', 'Bungalow', 'Townhouse', 'Standalone'],
            'Commercial' => ['Hotel', 'Hostel', 'Office', 'Shop'],
            'Industrial' => ['Godown'],
            'Land' => ['Land'],
        ];

        foreach ($subTypes as $typeName => $subTypeNames) {
            $type = PropertyType::where('name', $typeName)->first();
            if ($type) {
                foreach ($subTypeNames as $subTypeName) {
                    PropertySubType::updateOrCreate([
                        'property_type_id' => $type->id,
                        'name' => $subTypeName,
                    ]);
                }
            }
        }
    }
}
