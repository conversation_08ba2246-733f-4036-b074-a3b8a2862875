<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\PropertyType;
use Illuminate\Database\Seeder;

final class PropertyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            'Residential',
            'Commercial',
            'Industrial',
            'Land',
        ];

        foreach ($types as $type) {
            PropertyType::updateOrCreate(['name' => $type]);
        }
    }
}
