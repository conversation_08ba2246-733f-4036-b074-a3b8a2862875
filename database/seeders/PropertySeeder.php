<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Amenity;
use App\Models\Property;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

final class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = Amenity::all();
        if ($amenities->isEmpty()) {
            $this->command->warn('No amenities found. Please run the AmenitySeeder first.');

            return;
        }

        $imagePath = storage_path('app/public/properties');
        if (! File::exists($imagePath)) {
            File::makeDirectory($imagePath, 0755, true);
            $this->command->info('Created directory: storage/app/public/properties');
        }
        $images = File::files($imagePath);

        $agents = User::role('agent')->get();
        if ($agents->isEmpty()) {
            $this->command->warn('No agents found. Please run the UserRoleSeeder first.');

            return;
        }

        Property::factory(50)->make()->each(function ($property) use ($agents, $amenities, $images): void {
            $property->user_id = $agents->random()->id;
            $property->save();

            $randomAmenities = $amenities->random(random_int(1, 5));
            $property->amenities()->attach($randomAmenities);

            if (! empty($images)) {
                $randomImages = collect($images)->random(random_int(1, min(5, count($images))));
                foreach ($randomImages as $image) {
                    $property->addMedia($image->getPathname())
                        ->preservingOriginal()
                        ->toMediaCollection('gallery');
                }
            }
        });

        $this->command->info('Created 50 properties, assigned to agents, attached amenities, and added images.');
    }
}
