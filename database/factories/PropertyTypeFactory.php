<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PropertyType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PropertyType>
 */
final class PropertyTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PropertyType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement(['Residential', 'Commercial', 'Land', 'Industrial']),
        ];
    }

    /**
     * Create a residential property type.
     */
    public function residential(): static
    {
        return $this->state(fn (array $attributes): array => [
            'name' => 'Residential',
        ]);
    }

    /**
     * Create a commercial property type.
     */
    public function commercial(): static
    {
        return $this->state(fn (array $attributes): array => [
            'name' => 'Commercial',
        ]);
    }

    /**
     * Create a land property type.
     */
    public function land(): static
    {
        return $this->state(fn (array $attributes): array => [
            'name' => 'Land',
        ]);
    }
}
