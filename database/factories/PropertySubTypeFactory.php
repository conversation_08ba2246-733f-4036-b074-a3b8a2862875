<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PropertySubType;
use App\Models\PropertyType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PropertySubType>
 */
final class PropertySubTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PropertySubType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'property_type_id' => PropertyType::factory(),
            'name' => fake()->randomElement([
                'Single Family Home',
                'Townhouse',
                'Condominium',
                'Apartment',
                'Office Building',
                'Retail Space',
                'Warehouse',
                'Vacant Land',
                'Agricultural Land',
            ]),
        ];
    }

    /**
     * Create a residential sub-type.
     */
    public function residential(): static
    {
        return $this->state(fn (array $attributes): array => [
            'property_type_id' => PropertyType::factory()->residential(),
            'name' => fake()->randomElement([
                'Single Family Home',
                'Townhouse',
                'Condominium',
                'Apartment',
            ]),
        ]);
    }

    /**
     * Create a commercial sub-type.
     */
    public function commercial(): static
    {
        return $this->state(fn (array $attributes): array => [
            'property_type_id' => PropertyType::factory()->commercial(),
            'name' => fake()->randomElement([
                'Office Building',
                'Retail Space',
                'Warehouse',
                'Restaurant',
            ]),
        ]);
    }

    /**
     * Create a land sub-type.
     */
    public function land(): static
    {
        return $this->state(fn (array $attributes): array => [
            'property_type_id' => PropertyType::factory()->land(),
            'name' => fake()->randomElement([
                'Vacant Land',
                'Agricultural Land',
                'Development Land',
            ]),
        ]);
    }
}
