<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\PropertySubType;
use App\Models\PropertyType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Property>
 */
final class PropertyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subType = PropertySubType::with('propertyType')->inRandomOrder()->first();

        // If no PropertySubType exists (like in tests), create one
        if (! $subType) {
            $propertyType = PropertyType::firstOrCreate(['name' => 'Residential']);
            $subType = PropertySubType::firstOrCreate([
                'name' => 'House',
                'property_type_id' => $propertyType->id,
            ]);
            $subType->load('propertyType');
        }

        $isLand = $subType->propertyType->name === 'Land';

        return [
            'user_id' => User::inRandomOrder()->first()?->id ?? User::factory()->create()->id,
            'property_sub_type_id' => $subType->id,
            'listing_type' => $this->faker->randomElement(['for_sale', 'for_rent']),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(3),
            'price' => $this->faker->numberBetween(50000, 2000000),
            'currency' => 'USD',
            'address_line_1' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state_region' => $this->faker->stateAbbr,
            'zip_code' => $this->faker->postcode,
            'bedrooms' => $isLand ? null : $this->faker->numberBetween(1, 6),
            'bathrooms' => $isLand ? null : $this->faker->numberBetween(1, 4),
            'square_footage' => $isLand ? null : $this->faker->numberBetween(500, 5000),
            'lot_size_acres' => $isLand ? $this->faker->randomFloat(2, 1, 100) : null,
            'zoning_details' => $isLand ? $this->faker->randomElement(['Residential', 'Commercial', 'Agricultural']) : null,
            'road_access' => $isLand && $this->faker->boolean(),
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'status' => $this->faker->randomElement(['published', 'draft', 'sold', 'rented']),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
        ];
    }
}
