/* Admin Dashboard Custom Styles */

/* Layout Structure */
.admin-layout {
    @apply h-full bg-gray-50;
}

.admin-sidebar {
    @apply w-64 border-r border-gray-200 bg-white shadow-sm;
}

.admin-main-content {
    @apply flex-1 flex flex-col min-h-0;
}

.admin-content-area {
    @apply flex-1 overflow-y-auto bg-gray-50 p-6;
}

/* Navigation Styles */
.nav-group {
    @apply mb-6;
}

.nav-group-title {
    @apply text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3;
}

.nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-150;
}

.nav-item:hover {
    @apply bg-gray-100 text-gray-900;
}

.nav-item.active {
    @apply bg-blue-100 text-blue-700 border-r-2 border-blue-700;
}

.nav-item-icon {
    @apply mr-3 h-5 w-5 flex-shrink-0;
}

/* Dashboard Cards */
.dashboard-card {
    @apply bg-white rounded-lg shadow border p-6 transition-shadow duration-200;
}

.dashboard-card:hover {
    @apply shadow-md;
}

.dashboard-card-header {
    @apply flex items-center justify-between mb-4;
}

.dashboard-card-title {
    @apply text-lg font-semibold text-gray-900;
}

.dashboard-card-subtitle {
    @apply text-sm text-gray-600;
}

.dashboard-card-icon {
    @apply h-8 w-8 text-gray-400;
}

/* Metric Cards */
.metric-card {
    @apply bg-white rounded-lg shadow border p-6;
}

.metric-value {
    @apply text-3xl font-bold;
}

.metric-label {
    @apply text-sm font-medium text-gray-600 mt-1;
}

.metric-sublabel {
    @apply text-xs text-gray-500 mt-1;
}

/* Progress Bars */
.progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-fill {
    @apply h-2 rounded-full transition-all duration-300;
}

/* Status Badges */
.status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.published {
    @apply bg-green-100 text-green-800;
}

.status-badge.draft {
    @apply bg-yellow-100 text-yellow-800;
}

.status-badge.sold {
    @apply bg-blue-100 text-blue-800;
}

.status-badge.rented {
    @apply bg-purple-100 text-purple-800;
}

.status-badge.under-offer {
    @apply bg-orange-100 text-orange-800;
}

/* Role Badges */
.role-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.role-badge.admin {
    @apply bg-purple-100 text-purple-800;
}

.role-badge.lister {
    @apply bg-indigo-100 text-indigo-800;
}

.role-badge.seeker {
    @apply bg-teal-100 text-teal-800;
}

/* Activity Feed */
.activity-item {
    @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg;
}

.activity-avatar {
    @apply flex-shrink-0 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-medium;
}

.activity-content {
    @apply flex-1 min-w-0;
}

.activity-title {
    @apply text-sm font-medium text-gray-900 truncate;
}

.activity-subtitle {
    @apply text-xs text-gray-500;
}

/* Tables */
.admin-table {
    @apply min-w-full divide-y divide-gray-200;
}

.admin-table thead {
    @apply bg-gray-50;
}

.admin-table th {
    @apply px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-table td {
    @apply px-4 py-3 text-sm text-gray-900;
}

.admin-table tbody tr:hover {
    @apply bg-gray-50;
}

/* Compact Table Styles */
.admin-table-compact th {
    @apply px-3 py-2;
}

.admin-table-compact td {
    @apply px-3 py-2;
}

/* Text Truncation Utilities */
.truncate-sm {
    @apply truncate max-w-24;
}

.truncate-md {
    @apply truncate max-w-32;
}

.truncate-lg {
    @apply truncate max-w-48;
}

.truncate-xl {
    @apply truncate max-w-64;
}

/* Action Button Groups */
.action-group {
    @apply flex items-center space-x-1;
}

.action-btn {
    @apply p-1.5 text-gray-400 hover:text-gray-600 transition-colors duration-150 rounded;
}

.action-btn:hover {
    @apply bg-gray-100;
}

.action-btn.edit {
    @apply hover:text-blue-600;
}

.action-btn.delete {
    @apply hover:text-red-600;
}

.action-btn.featured {
    @apply hover:text-yellow-500;
}

.action-btn.featured.active {
    @apply text-yellow-500;
}

/* Buttons */
.btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150;
}

.btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150;
}

/* Forms */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
}

.form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        @apply fixed inset-y-0 left-0 z-50 transform -translate-x-full transition-transform duration-300;
    }
    
    .admin-sidebar.open {
        @apply translate-x-0;
    }
    
    .admin-main-content {
        @apply w-full;
    }
}

@media (max-width: 768px) {
    .admin-content-area {
        @apply p-4;
    }
    
    .dashboard-card {
        @apply p-4;
    }
    
    .metric-value {
        @apply text-2xl;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-layout {
        @apply bg-gray-900;
    }
    
    .admin-sidebar {
        @apply bg-gray-800 border-gray-700;
    }
    
    .dashboard-card {
        @apply bg-gray-800 border-gray-700;
    }
    
    .nav-item {
        @apply text-gray-300;
    }
    
    .nav-item:hover {
        @apply bg-gray-700 text-white;
    }
    
    .nav-item.active {
        @apply bg-blue-900 text-blue-300;
    }
}
