<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @auth
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if(Auth::user()->hasRole('agent') || Auth::user()->hasRole('developer') || Auth::user()->hasRole('owner'))
                                <div class="col-span-1 md:col-span-2">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __("Lister Dashboard") }}</h3>
                                    <p class="mb-4">{{ __("Welcome! Manage your properties and client interactions from here.") }}</p>
                                    <a href="{{ route('profile.edit') }}" class="text-blue-600 hover:underline inline-block mb-4">{{ __("Edit Your Profile") }}</a>
                                </div>
                                
                                <!-- Quick Actions for Agents, Developers, and Owners -->
                                <div class="bg-blue-50 p-4 rounded-lg shadow-sm">
                                    <h4 class="font-medium text-blue-800 mb-3">{{ __("Quick Actions") }}</h4>
                                    <div class="flex flex-wrap gap-3">
                                        <a href="{{ route('agent.properties.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            {{ __("Create New Property") }}
                                        </a>
                                        <a href="{{ route('agent.properties.index') }}" class="inline-flex items-center px-4 py-2 bg-white border border-blue-300 rounded-md font-semibold text-xs text-blue-700 uppercase tracking-widest shadow-sm hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150">
                                            {{ __("View My Properties") }}
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- Lister Stats or Summary -->
                                <div class="bg-green-50 p-4 rounded-lg shadow-sm">
                                    <h4 class="font-medium text-green-800 mb-3">{{ __("Your Listings") }}</h4>
                                    <p class="text-gray-600">{{ __("You have :count properties listed.", ['count' => Auth::user()->properties->count()]) }}</p>
                                    @if(Auth::user()->properties->count() > 0)
                                        <a href="{{ route('agent.properties.index') }}" class="text-sm text-green-600 hover:underline mt-2 inline-block">{{ __("Manage Listings") }}</a>
                                    @endif
                                </div>
                                
                                <div class="col-span-1 md:col-span-2">
                                    <livewire:my-properties />
                                </div>
                                
                                <div class="col-span-1 md:col-span-2">
                                    <livewire:favorite-properties />
                                </div>
                                
                            @elseif(Auth::user()->hasRole('seeker'))
                                <div class="col-span-1 md:col-span-2">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __("Seeker Dashboard") }}</h3>
                                    <p class="mb-4">{{ __("Welcome, Seeker! Find your dream property or manage your favorites.") }}</p>
                                </div>
                                
                                <!-- Quick Actions for Seekers -->
                                <div class="bg-blue-50 p-4 rounded-lg shadow-sm">
                                    <h4 class="font-medium text-blue-800 mb-3">{{ __("Quick Actions") }}</h4>
                                    <div class="flex flex-wrap gap-3">
                                        <a href="{{ route('properties.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            {{ __("Search Properties") }}
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- Seeker Preferences -->
                                <div class="bg-purple-50 p-4 rounded-lg shadow-sm">
                                    <h4 class="font-medium text-purple-800 mb-3">{{ __("Your Preferences") }}</h4>
                                    <p class="text-gray-600 text-sm">
                                        @if(Auth::user()->budget_min && Auth::user()->budget_max)
                                            {{ __("Budget: :min - :max", ['min' => number_format(Auth::user()->budget_min), 'max' => number_format(Auth::user()->budget_max)]) }}
                                        @else
                                            {{ __("Budget: Not set") }}
                                        @endif
                                    </p>
                                    <p class="text-gray-600 text-sm">
                                        @if(Auth::user()->search_radius_km)
                                            {{ __("Search Radius: :radius km", ['radius' => Auth::user()->search_radius_km]) }}
                                        @else
                                            {{ __("Search Radius: Not set") }}
                                        @endif
                                    </p>
                                    <a href="{{ route('profile.edit') }}" class="text-sm text-purple-600 hover:underline mt-2 inline-block">{{ __("Update Preferences") }}</a>
                                </div>
                                
                                <div class="col-span-1 md:col-span-2">
                                    <livewire:favorite-properties />
                                </div>
                                
                            @else
                                <div class="col-span-1 md:col-span-2">
                                    <p>{{ __("You're logged in!") }}</p>
                                </div>
                                
                                <div class="col-span-1 md:col-span-2">
                                    <livewire:favorite-properties />
                                </div>
                            @endif
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
