<x-layouts.app title="Property Management">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Management</h1>
        <p class="mt-2 text-gray-600">Manage all property listings and their status</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        @if (session('success'))
            <flux:callout variant="success" icon="check-circle">
                {{ session('success') }}
            </flux:callout>
        @endif

        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Search & Filter</flux:heading>
            </div>

            <form action="{{ route('admin.properties.index') }}" method="GET" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <flux:field>
                            <flux:label>Keywords</flux:label>
                            <flux:input name="keywords" value="{{ request('keywords') }}" placeholder="Search properties..." />
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Property Type</flux:label>
                            <flux:select name="property_type_id">
                                <option value="" {{ request('property_type_id') == '' ? 'selected' : '' }}>All Types</option>
                                @foreach ($propertyTypes as $type)
                                    <option value="{{ $type->id }}" {{ request('property_type_id') == $type->id ? 'selected' : '' }}>{{ $type->name }}</option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Status</flux:label>
                            <flux:select name="status">
                                <option value="" {{ request('status') == '' ? 'selected' : '' }}>All Statuses</option>
                                <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                                <option value="sold" {{ request('status') == 'sold' ? 'selected' : '' }}>Sold</option>
                                <option value="rented" {{ request('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                                <option value="under_offer" {{ request('status') == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                            </flux:select>
                        </flux:field>
                    </div>

                    <div class="flex items-end">
                        <flux:button type="submit" variant="primary" class="w-full">
                            <flux:icon.magnifying-glass class="size-4" />
                            Filter
                        </flux:button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Properties Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($properties as $property)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->title }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->property_type }} ({{ $property->listing_type }})</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $property->currency }} {{ number_format($property->price) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <flux:badge :variant="$property->status->value == 'published' ? 'success' : ($property->status->value == 'draft' ? 'warning' : 'secondary')">
                                        {{ ucfirst(str_replace('_', ' ', $property->status->value)) }}
                                    </flux:badge>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <!-- Edit button with pencil icon -->
                                        <a href="{{ route('admin.properties.edit', $property->id) }}" class="p-1 text-blue-300 hover:text-blue-600">
                                            <flux:icon.pencil-square />
                                        </a>
                                        
                                        <!-- Featured button with star icon -->
                                        <form action="{{ route('admin.properties.toggleFeatured', $property->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="p-1 {{ $property->is_featured ? 'text-yellow-500' : 'text-gray-400' }} hover:text-yellow-700">
                                                <flux:icon.star />
                                            </button>
                                        </form>
                                        
                                        <!-- Delete button with trash icon -->
                                        <form action="{{ route('admin.properties.destroy', $property->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="p-2 text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this property?')">
                                                <flux:icon.trash />
                                            </button>
                                        </form>
                                        
                                        <!-- Status dropdown -->
                                        <form action="{{ route('admin.properties.updateStatus', $property->id) }}" method="POST" class="inline-block">
                                            @csrf
                                            @method('PATCH')
                                            <flux:select name="status" size="sm" onchange="this.form.submit()">
                                                <option value="draft" {{ $property->status->value == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ $property->status->value == 'published' ? 'selected' : '' }}>Publish</option>
                                                <option value="sold" {{ $property->status->value == 'sold' ? 'selected' : '' }}>Sold</option>
                                                <option value="rented" {{ $property->status->value == 'rented' ? 'selected' : '' }}>Rented</option>
                                                <option value="under_offer" {{ $property->status->value == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                                            </flux:select>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="p-6 border-t border-gray-200">
                {{ $properties->links() }}
            </div>
        </div>
    </div>
</x-layouts.app>
