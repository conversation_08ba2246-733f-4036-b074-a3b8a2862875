<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
    <head>
        @include('partials.head')
    </head>
    <body class="h-full bg-gray-50">
        <div class="flex h-full">
            <!-- Sidebar -->
            <flux:sidebar sticky stashable class="w-64 border-r border-gray-200 bg-white">
                <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

                <!-- Logo -->
                <div class="flex items-center px-6 py-4 border-b border-gray-200">
                    <a href="{{ route('dashboard') }}" class="flex items-center space-x-2" wire:navigate>
                        <x-app-logo />
                    </a>
                </div>

                <!-- Navigation -->
                <div class="flex-1 px-4 py-6 overflow-y-auto">
                    <flux:navlist variant="outline" class="space-y-1">
                        <flux:navlist.group :heading="__('Platform')" class="mb-6">
                            <flux:navlist.item icon="layout-grid" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate class="mb-1">{{ __('Dashboard') }}</flux:navlist.item>
                            <flux:navlist.item icon="home" :href="route('properties.index')" :current="request()->routeIs('properties.index')" wire:navigate class="mb-1">{{ __('Properties') }}</flux:navlist.item>
                            @auth
                                @if (auth()->user()->hasRole('agent'))
                                    <flux:navlist.item icon="building-office" :href="route('agent.properties.index')" :current="request()->routeIs('agent.properties.*')" wire:navigate class="mb-1">{{ __('My Properties') }}</flux:navlist.item>
                                    <flux:navlist.item icon="plus-circle" :href="route('agent.properties.create')" :current="request()->routeIs('agent.properties.create')" wire:navigate class="mb-1">{{ __('Create Listing') }}</flux:navlist.item>
                                @endif
                                @if (auth()->user()->hasRole('admin'))
                                    <flux:navlist.item icon="shield" :href="route('admin.dashboard')" :current="request()->routeIs('admin.*')" wire:navigate class="mb-1">{{ __('Admin Dashboard') }}</flux:navlist.item>
                                @endif
                            @endauth
                        </flux:navlist.group>

                        @auth
                            @if (auth()->user()->hasRole('admin'))
                                <flux:navlist.group :heading="__('Administration')" class="mb-6">
                                    <flux:navlist.item icon="users" :href="route('admin.users.index')" :current="request()->routeIs('admin.users.*')" wire:navigate class="mb-1">{{ __('User Management') }}</flux:navlist.item>
                                    <flux:navlist.item icon="building-office" :href="route('admin.properties.index')" :current="request()->routeIs('admin.properties.*')" wire:navigate class="mb-1">{{ __('Property Management') }}</flux:navlist.item>
                                </flux:navlist.group>

                                <flux:navlist.group :heading="__('Analytics & Reports')" class="mb-6">
                                    <flux:navlist.item icon="chart-bar" :href="route('admin.dashboard')" :current="request()->routeIs('admin.dashboard')" wire:navigate class="mb-1">{{ __('Dashboard Analytics') }}</flux:navlist.item>
                                    <flux:navlist.item icon="users" :href="route('admin.reports.users')" :current="request()->routeIs('admin.reports.users')" wire:navigate class="mb-1">{{ __('User Reports') }}</flux:navlist.item>
                                    <flux:navlist.item icon="home" :href="route('admin.reports.properties')" :current="request()->routeIs('admin.reports.properties')" wire:navigate class="mb-1">{{ __('Property Reports') }}</flux:navlist.item>
                                </flux:navlist.group>

                                <flux:navlist.group :heading="__('System')" class="mb-6">
                                    <flux:navlist.item icon="cog" :href="route('admin.settings.index')" :current="request()->routeIs('admin.settings.*')" wire:navigate class="mb-1">{{ __('Settings') }}</flux:navlist.item>
                                    <!-- Security Logs -->
                                    <!-- System health -->
                                </flux:navlist.group>
                            @endif
                        @endauth
                    </flux:navlist>
                </div>

                <!-- Bottom Section -->
                <div class="border-t border-gray-200 p-4">
                    <!-- Quick Links -->
                    <div class="mb-4">
                        <flux:navlist variant="outline" class="space-y-1">
                            <flux:navlist.item icon="home" :href="route('home')" wire:navigate class="text-sm">
                                {{ __('Homepage') }}
                            </flux:navlist.item>
                            <flux:navlist.item icon="question-mark-circle" href="mailto:<EMAIL>" class="text-sm">
                                {{ __('Support') }}
                            </flux:navlist.item>
                        </flux:navlist>
                    </div>

                    <!-- User Profile -->
                    @auth
                        <flux:dropdown position="top" align="start" class="w-full">
                            <div class="flex items-center w-full p-3 rounded-lg bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors">
                                <div class="flex h-8 w-8 shrink-0 overflow-hidden rounded-lg bg-blue-600 items-center justify-center text-white text-sm font-medium mr-3">
                                    {{ auth()->user()->initials() }}
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900 truncate">{{ auth()->user()->name }}</div>
                                    <div class="text-xs text-gray-500 truncate">{{ auth()->user()->email }}</div>
                                </div>
                                <flux:icon.chevrons-up-down class="h-4 w-4 text-gray-400" />
                            </div>

                            <flux:menu class="w-56">
                                <flux:menu.radio.group>
                                    <div class="p-0 text-sm font-normal">
                                        <div class="flex items-center gap-2 px-3 py-2 text-start text-sm border-b border-gray-100">
                                            <div class="flex h-8 w-8 shrink-0 overflow-hidden rounded-lg bg-blue-600 items-center justify-center text-white text-sm font-medium">
                                                {{ auth()->user()->initials() }}
                                            </div>
                                            <div class="grid flex-1 text-start text-sm leading-tight">
                                                <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                                <span class="truncate text-xs text-gray-500">{{ auth()->user()->email }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </flux:menu.radio.group>

                                <flux:menu.radio.group>
                                    <flux:menu.item :href="route('profile.edit')" icon="user" wire:navigate>{{ __('Profile') }}</flux:menu.item>
                                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                                    @if (auth()->user()->hasRole('agent'))
                                        <flux:menu.item :href="route('agent.properties.index')" icon="building-office" wire:navigate>{{ __('My Properties') }}</flux:menu.item>
                                        <flux:menu.item :href="route('agent.properties.create')" icon="plus-circle" wire:navigate>{{ __('Create Listing') }}</flux:menu.item>
                                    @endif
                                    @if (auth()->user()->hasRole('admin'))
                                        <flux:menu.item :href="route('admin.dashboard')" icon="shield" wire:navigate>{{ __('Admin Dashboard') }}</flux:menu.item>
                                    @endif
                                </flux:menu.radio.group>

                                <flux:menu.separator />

                                <form method="POST" action="{{ route('logout') }}" class="w-full">
                                    @csrf
                                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                        {{ __('Log Out') }}
                                    </flux:menu.item>
                                </form>
                            </flux:menu>
                        </flux:dropdown>
                    @endauth
                </div>
            </flux:sidebar>

            <!-- Main Content Area -->
            <div class="flex-1 flex flex-col min-h-0">
                <!-- Mobile Header -->
                <flux:header class="lg:hidden border-b border-gray-200 bg-white">
                    <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

                    <div class="flex items-center ml-4">
                        <x-app-logo />
                    </div>

                    <flux:spacer />

                    @auth
                        <flux:dropdown position="bottom" align="end">
                            <div class="flex h-8 w-8 shrink-0 overflow-hidden rounded-lg bg-blue-600 items-center justify-center text-white text-sm font-medium cursor-pointer">
                                {{ auth()->user()->initials() }}
                            </div>

                            <flux:menu>
                                <flux:menu.radio.group>
                                    <div class="p-0 text-sm font-normal">
                                        <div class="flex items-center gap-2 px-3 py-2 text-start text-sm border-b border-gray-100">
                                            <div class="flex h-8 w-8 shrink-0 overflow-hidden rounded-lg bg-blue-600 items-center justify-center text-white text-sm font-medium">
                                                {{ auth()->user()->initials() }}
                                            </div>
                                            <div class="grid flex-1 text-start text-sm leading-tight">
                                                <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                                <span class="truncate text-xs text-gray-500">{{ auth()->user()->email }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </flux:menu.radio.group>

                                <flux:menu.radio.group>
                                    <flux:menu.item :href="route('profile.edit')" icon="user" wire:navigate>{{ __('Profile') }}</flux:menu.item>
                                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                                    @if (auth()->user()->hasRole('agent'))
                                        <flux:menu.item :href="route('agent.properties.index')" icon="building-office" wire:navigate>{{ __('My Properties') }}</flux:menu.item>
                                        <flux:menu.item :href="route('agent.properties.create')" icon="plus-circle" wire:navigate>{{ __('Create Listing') }}</flux:menu.item>
                                    @endif
                                    @if (auth()->user()->hasRole('admin'))
                                        <flux:menu.item :href="route('admin.dashboard')" icon="shield" wire:navigate>{{ __('Admin Dashboard') }}</flux:menu.item>
                                    @endif
                                </flux:menu.radio.group>

                                <flux:menu.separator />

                                <form method="POST" action="{{ route('logout') }}" class="w-full">
                                    @csrf
                                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                        {{ __('Log Out') }}
                                    </flux:menu.item>
                                </form>
                            </flux:menu>
                        </flux:dropdown>
                    @endauth
                </flux:header>

                <!-- Main Content -->
                <main class="flex-1 overflow-y-auto bg-gray-50">
                    <div class="p-6">
                        {{ $slot }}
                    </div>
                </main>
            </div>
        </div>
        @fluxScripts
    </body>
</html>
