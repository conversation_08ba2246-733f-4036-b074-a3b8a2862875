<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Profile Information') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __("Update your account's profile information and email address.") }}
        </p>
    </header>

    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    <form method="post" action="{{ route('profile.update') }}" class="mt-6 space-y-6">
        @csrf
        @method('patch')

        <div>
            <flux:input id="name" name="name" type="text" label="{{ __('Name') }}" class="mt-1 block w-full" :value="old('name', $user->name)" required autofocus autocomplete="name" />
        </div>

        <div>
            <flux:input id="email" name="email" type="email" label="{{ __('Email') }}" class="mt-1 block w-full" :value="old('email', $user->email)" required autocomplete="username" />

            @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                <div>
                    <p class="text-sm mt-2 text-gray-800">
                        {{ __('Your email address is unverified.') }}

                        <button form="send-verification" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {{ __('Click here to re-send the verification email.') }}
                        </button>
                    </p>

                    @if (session('status') === 'verification-link-sent')
                        <p class="mt-2 font-medium text-sm text-green-600">
                            {{ __('A new verification link has been sent to your email address.') }}
                        </p>
                    @endif
                </div>
            @endif
        </div>

        <div class="mt-6">
            <label for="profile_photo" class="block text-sm font-medium text-gray-700">{{ __('Profile Photo') }}</label>
            <div class="mt-2 flex items-center">
                @if($user->getFirstMediaUrl('profile_photo'))
                    <img src="{{ $user->getFirstMediaUrl('profile_photo') }}" alt="{{ $user->name }}" class="w-20 h-20 rounded-full object-cover mr-4">
                    <div class="flex flex-col">
                        <input id="profile_photo" name="profile_photo" type="file" class="mt-2 block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" accept="image/*">
                        <p class="mt-1 text-xs text-gray-500">{{ __('Upload a new photo to replace the current one.') }}</p>
                    </div>
                @else
                    <div class="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                        <span class="text-gray-500">{{ $user->initials() }}</span>
                    </div>
                    <div class="flex flex-col">
                        <input id="profile_photo" name="profile_photo" type="file" class="mt-2 block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" accept="image/*">
                        <p class="mt-1 text-xs text-gray-500">{{ __('Upload a profile photo.') }}</p>
                    </div>
                @endif
            </div>
            @error('profile_photo')
                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        @if (auth()->user()->hasRole('agent'))
            <div>
                <flux:input id="phone" name="phone" type="text" label="{{ __('Phone Number') }}" class="mt-1 block w-full" :value="old('phone', $user->phone)" autocomplete="tel" />
            </div>

            <div class="mt-6">
                <flux:input id="agency_name" name="agency_name" type="text" label="{{ __('Agency Name') }}" class="mt-1 block w-full" :value="old('agency_name', $user->agency_name)" />
            </div>

            <div class="mt-6">
                <flux:input id="license_number" name="license_number" type="text" label="{{ __('License Number') }}" class="mt-1 block w-full" :value="old('license_number', $user->license_number)" />
            </div>
        @endif

        @if (auth()->user()->hasRole('seeker'))
            <div class="mt-6">
                <flux:input id="budget_min" name="budget_min" type="number" label="{{ __('Minimum Budget') }}" class="mt-1 block w-full" :value="old('budget_min', $user->budget_min)" />
            </div>

            <div class="mt-6">
                <flux:input id="budget_max" name="budget_max" type="number" label="{{ __('Maximum Budget') }}" class="mt-1 block w-full" :value="old('budget_max', $user->budget_max)" />
            </div>

            <div class="mt-6">
                <flux:input id="search_radius_km" name="search_radius_km" type="number" label="{{ __('Search Radius (km)') }}" class="mt-1 block w-full" :value="old('search_radius_km', $user->search_radius_km)" />
            </div>

            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">{{ __('Search Location') }}</label>
                <div id="searchLocationMap" style="height: 300px;" class="mt-2 rounded-lg border border-gray-300 shadow-sm"
                     data-latitude="{{ old('search_latitude', $user->search_latitude) ?: 0 }}"
                     data-longitude="{{ old('search_longitude', $user->search_longitude) ?: 0 }}"></div>
                <div class="mt-2 grid grid-cols-2 gap-4">
                    <flux:input id="search_latitude" name="search_latitude" type="number" step="0.000001" label="{{ __('Latitude') }}" class="mt-1 block w-full" :value="old('search_latitude', $user->search_latitude)" />
                    <flux:input id="search_longitude" name="search_longitude" type="number" step="0.000001" label="{{ __('Longitude') }}" class="mt-1 block w-full" :value="old('search_longitude', $user->search_longitude)" />
                </div>
                <p class="mt-1 text-xs text-gray-500">{{ __('Drag the marker on the map to set your search center location.') }}</p>
            </div>

            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Notification Preferences') }}</label>
                <flux:input id="notify_property_type" name="notify_property_type" type="text" label="{{ __('Preferred Property Type') }}" class="mt-1 block w-full" :value="old('notify_property_type', $user->notify_property_type)" placeholder="e.g., Residential, Commercial, Land" />
            </div>

            <div class="mt-6 grid grid-cols-2 gap-4">
                <flux:input id="notify_budget_min" name="notify_budget_min" type="number" label="{{ __('Notification Budget Min') }}" class="mt-1 block w-full" :value="old('notify_budget_min', $user->notify_budget_min)" />
                <flux:input id="notify_budget_max" name="notify_budget_max" type="number" label="{{ __('Notification Budget Max') }}" class="mt-1 block w-full" :value="old('notify_budget_max', $user->notify_budget_max)" />
            </div>
        @endif

        @if(auth()->user()->hasRole('agent') || auth()->user()->hasRole('developer') || auth()->user()->hasRole('owner'))
            <div class="mt-6">
                <label for="bio" class="block text-sm font-medium text-gray-700">{{ __('Bio') }}</label>
                <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ old('bio', $user->bio) }}</textarea>
                @error('bio')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="linkedin_url" name="linkedin_url" type="url" label="{{ __('LinkedIn URL') }}" class="mt-1 block w-full" :value="old('linkedin_url', $user->linkedin_url)" placeholder="https://linkedin.com/in/username" />
                @error('linkedin_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="facebook_url" name="facebook_url" type="url" label="{{ __('Facebook URL') }}" class="mt-1 block w-full" :value="old('facebook_url', $user->facebook_url)" placeholder="https://facebook.com/username" />
                @error('facebook_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="twitter_url" name="twitter_url" type="url" label="{{ __('Twitter URL') }}" class="mt-1 block w-full" :value="old('twitter_url', $user->twitter_url)" placeholder="https://twitter.com/username" />
                @error('twitter_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        @endif

        <div class="flex items-center gap-4">
            <flux:button variant="primary">{{ __('Save') }}</flux:button>

            @if (session('status') === 'profile-updated')
                <p
                    x-data="{ show: true }"
                    x-show="show"
                    x-transition
                    x-init="setTimeout(() => show = false, 2000)"
                    class="text-sm text-gray-600"
                >{{ __('Saved.') }}</p>
            @endif
        </div>
    </form>
    @push('scripts')
        @if(auth()->user()->hasRole('seeker'))
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const mapElement = document.getElementById('searchLocationMap');
                    if (mapElement) {
                        const lat = parseFloat(mapElement.dataset.latitude) || 0;
                        const lng = parseFloat(mapElement.dataset.longitude) || 0;

                        const map = L.map(mapElement).setView([lat, lng], 10);
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        }).addTo(map);

                        const marker = L.marker([lat, lng], { draggable: true }).addTo(map);
                        marker.on('dragend', function(event) {
                            const position = event.target.getLatLng();
                            document.getElementById('search_latitude').value = position.lat.toFixed(6);
                            document.getElementById('search_longitude').value = position.lng.toFixed(6);
                        });

                        map.on('click', function(event) {
                            const lat = event.latlng.lat;
                            const lng = event.latlng.lng;
                            marker.setLatLng([lat, lng]);
                            document.getElementById('search_latitude').value = lat.toFixed(6);
                            document.getElementById('search_longitude').value = lng.toFixed(6);
                        });
                    }
                });
            </script>
        @endif
    @endpush
    </form>
</section>
