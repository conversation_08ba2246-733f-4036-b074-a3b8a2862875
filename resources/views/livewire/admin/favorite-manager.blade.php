<div>
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h2 class="text-xl font-semibold mb-4">Favorite Properties Management</h2>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Favorited By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($properties as $property)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($property->hasMedia('gallery'))
                                <img src="{{ $property->getFirstMediaUrl('gallery', 'thumb') }}" alt="{{ $property->title }}" class="w-16 h-16 object-cover rounded">
                            @else
                                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16"></div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $property->title }}</div>
                            <div class="text-sm text-gray-500">{{ $property->property_type }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $property->favoritedBy->count() }} users</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button 
                                wire:click="toggleFavorite({{ $property->id }})"
                                class="px-4 py-2 rounded {{ in_array($property->id, $favorites) ? 'bg-red-500 hover:bg-red-700' : 'bg-blue-500 hover:bg-blue-700' }} text-white"
                            >
                                {{ in_array($property->id, $favorites) ? 'Remove from Favorites' : 'Add to Favorites' }}
                            </button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
