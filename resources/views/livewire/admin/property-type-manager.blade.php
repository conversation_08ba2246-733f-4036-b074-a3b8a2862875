<div>
    <div class="bg-white rounded-2xl shadow-sm p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ $is_editing ? 'Edit Property Type' : 'Create Property Type' }}</h2>

        @if (session()->has('message'))
            <flux:callout variant="success" class="mb-4">
                {{ session('message') }}
            </flux:callout>
        @endif

        <form wire:submit.prevent="{{ $is_editing ? 'updatePropertyType' : 'savePropertyType' }}">
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <flux:input id="name" wire:model.defer="name" label="Name" />
                    @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="mt-6">
                <flux:button type="submit" variant="primary" loading="true">
                    <span wire:loading.remove wire:target="{{ $is_editing ? 'updatePropertyType' : 'savePropertyType' }}">
                        {{ $is_editing ? 'Update' : 'Save' }}
                    </span>
                    <span wire:loading wire:target="{{ $is_editing ? 'updatePropertyType' : 'savePropertyType' }}">
                        {{ $is_editing ? 'Updating...' : 'Saving...' }}
                    </span>
                </flux:button>
            </div>
        </form>
    </div>

    <div class="bg-white rounded-2xl shadow-sm p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Property Types</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Edit</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($propertyTypes as $propertyType)
                        <tr wire:key="property-type-{{ $propertyType->id }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $propertyType->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <flux:button wire:click="edit({{ $propertyType->id }})" size="sm" variant="ghost">
                                    Edit
                                </flux:button>
                                <flux:button wire:click="delete({{ $propertyType->id }})" wire:confirm="Are you sure you want to delete this property type?" size="sm" variant="danger">
                                    Delete
                                </flux:button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $propertyTypes->links() }}
        </div>
    </div>
</div>
