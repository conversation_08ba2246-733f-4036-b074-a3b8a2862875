<div>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Property Management</h1>
        <p class="mt-2 text-gray-600">Manage all property listings and their status</p>
    </div>

    <!-- Content -->
    <div class="space-y-6">
        <!-- Search and Filters -->
        <div class="bg-white p-6 rounded-lg shadow border">
            <div class="mb-6">
                <flux:heading size="xl">Search & Filter</flux:heading>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <flux:field>
                        <flux:label>Keywords</flux:label>
                        <flux:input wire:model.live.debounce.500ms="search" placeholder="Search properties..." />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Property Type</flux:label>
                        <flux:select wire:model.live="propertyTypeFilter" placeholder="All Types">
                            <option value="">All Types</option>
                            @foreach ($propertyTypes as $type)
                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Property Sub-Type</flux:label>
                        <flux:select wire:model.live="propertySubTypeFilter" placeholder="All Sub-Types" :disabled="!$propertyTypeFilter">
                            <option value="">All Sub-Types</option>
                            @foreach ($propertySubTypes as $subType)
                                <option value="{{ $subType->id }}">{{ $subType->name }}</option>
                            @endforeach
                        </flux:select>
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select wire:model.live="statusFilter" placeholder="All Statuses">
                            <option value="">All Statuses</option>
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                            <option value="sold">Sold</option>
                            <option value="rented">Rented</option>
                            <option value="under_offer">Under Offer</option>
                        </flux:select>
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Properties Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-16" wire:click="sortBy('id')">
                                ID
                                @if($sortField == 'id')
                                    <span class="ml-1">{{ $sortDirection == 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" wire:click="sortBy('title')">
                                Property
                                @if($sortField == 'title')
                                    <span class="ml-1">{{ $sortDirection == 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Agent</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Type</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32" wire:click="sortBy('price')">
                                Price
                                @if($sortField == 'price')
                                    <span class="ml-1">{{ $sortDirection == 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Status</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Media</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($properties as $property)
                            <tr wire:key="admin-property-{{ $property->id }}" class="hover:bg-gray-50">
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                    <span class="font-mono text-xs">#{{ $property->id }}</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="max-w-xs">
                                        <p class="text-sm font-medium text-gray-900 truncate" title="{{ $property->title }}">
                                            {{ $property->title }}
                                        </p>
                                        <p class="text-xs text-gray-500 truncate">
                                            {{ $property->city ?? 'Location not set' }}
                                        </p>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 truncate max-w-24" title="{{ $property->user->name }}">
                                        {{ $property->user->name }}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-xs">
                                        <span class="block font-medium text-gray-900">{{ $property->propertySubType->propertyType->name ?? 'N/A' }}</span>
                                        <span class="text-gray-500 capitalize">{{ str_replace('_', ' ', $property->listing_type) }}</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <span class="text-xs text-gray-500">{{ $property->currency }}</span>
                                        {{ number_format($property->price) }}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <flux:badge size="sm" :variant="
                                        $property->status->value == 'published' ? 'success' : (
                                        $property->status->value == 'draft' ? 'outline' : (
                                        $property->status->value == 'sold' ? 'danger' : (
                                        $property->status->value == 'rented' ? 'primary' : 'outline'
                                        )))
                                    ">
                                        {{ ucfirst(str_replace('_', ' ', $property->status->value)) }}
                                    </flux:badge>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    @php
                                        $mediaStatus = $this->getMediaStatus($property);
                                    @endphp
                                    <div class="flex items-center space-x-1">
                                        <div class="w-2 h-2 rounded-full bg-{{ $mediaStatus['color'] }}-500"></div>
                                        <span class="text-xs text-gray-600">
                                            {{ $mediaStatus['converted'] }}/{{ $mediaStatus['count'] }}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ $mediaStatus['label'] }}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center space-x-1">
                                        <!-- Edit button -->
                                        <a href="{{ route('admin.properties.edit', $property->id) }}"
                                           class="p-1.5 text-gray-400 hover:text-blue-600 transition-colors"
                                           title="Edit Property">
                                            <flux:icon.pencil-square class="size-4" />
                                        </a>

                                        <!-- Featured toggle -->
                                        <button wire:click="toggleFeatured({{ $property->id }})"
                                                class="p-1.5 transition-colors {{ $property->is_featured ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500' }}"
                                                title="{{ $property->is_featured ? 'Remove from Featured' : 'Add to Featured' }}">
                                            <flux:icon.star class="size-4 {{ $property->is_featured ? 'fill-current' : '' }}" />
                                        </button>

                                        <!-- Quick status dropdown -->
                                        <div class="relative">
                                            <flux:select wire:change="updateStatus({{ $property->id }}, $event.target.value)"
                                                        size="sm"
                                                        class="text-xs min-w-0 w-20">
                                                <option value="draft" {{ $property->status->value == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ $property->status->value == 'published' ? 'selected' : '' }}>Live</option>
                                                <option value="sold" {{ $property->status->value == 'sold' ? 'selected' : '' }}>Sold</option>
                                                <option value="rented" {{ $property->status->value == 'rented' ? 'selected' : '' }}>Rented</option>
                                                <option value="under_offer" {{ $property->status->value == 'under_offer' ? 'selected' : '' }}>Offer</option>
                                            </flux:select>
                                        </div>

                                        <!-- Delete button -->
                                        <button wire:click="deleteProperty({{ $property->id }})"
                                                wire:confirm="Are you sure you want to delete this property?"
                                                class="p-1.5 text-gray-400 hover:text-red-600 transition-colors"
                                                title="Delete Property">
                                            <flux:icon.trash class="size-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="p-6 border-t border-gray-200">
                {{ $properties->links() }}
            </div>
        </div>
    </div>
</div>
