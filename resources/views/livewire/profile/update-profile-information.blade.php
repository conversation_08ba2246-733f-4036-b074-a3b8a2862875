<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Profile Information') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __("Update your account's profile information and email address.") }}
        </p>
    </header>

    @if (session('status') === 'profile-updated')
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ __('Profile updated successfully!') }}
        </div>
    @endif

    <form wire:submit="updateProfileInformation" class="mt-6 space-y-6" enctype="multipart/form-data">
        <div>
            <flux:input id="name" name="name" type="text" label="{{ __('Name') }}" class="mt-1 block w-full" wire:model="name" required autofocus autocomplete="name" />
            @error('name') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
        </div>

        <div>
            <flux:input id="email" name="email" type="email" label="{{ __('Email') }}" class="mt-1 block w-full" wire:model="email" required autocomplete="username" />
            @error('email') <span class="text-sm text-red-600">{{ $message }}</span> @enderror

            @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                <div>
                    <p class="text-sm mt-2 text-gray-800">
                        {{ __('Your email address is unverified.') }}

                        <button type="button" wire:click="sendVerification" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {{ __('Click here to re-send the verification email.') }}
                        </button>
                    </p>

                    @if (session('status') === 'verification-link-sent')
                        <p class="mt-2 font-medium text-sm text-green-600">
                            {{ __('A new verification link has been sent to your email address.') }}
                        </p>
                    @endif
                </div>
            @endif
        </div>

        <div class="mt-6">
            <label for="profile_photo" class="block text-sm font-medium text-gray-700">{{ __('Profile Photo') }}</label>
            <div class="mt-2 flex items-center">
                @if($user->getFirstMediaUrl('profile_photo'))
                    <img src="{{ $user->getFirstMediaUrl('profile_photo') }}" alt="{{ $user->name }}" class="w-20 h-20 rounded-full object-cover mr-4">
                    <div class="flex flex-col">
                        <input id="profile_photo" name="profile_photo" type="file" class="mt-2 block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" accept="image/*" wire:model="profile_photo">
                        <p class="mt-1 text-xs text-gray-500">{{ __('Upload a new photo to replace the current one.') }}</p>
                    </div>
                @else
                    <div class="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                        <span class="text-gray-500">{{ $user->initials() }}</span>
                    </div>
                    <div class="flex flex-col">
                        <input id="profile_photo" name="profile_photo" type="file" class="mt-2 block text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" accept="image/*" wire:model="profile_photo">
                        <p class="mt-1 text-xs text-gray-500">{{ __('Upload a profile photo.') }}</p>
                    </div>
                @endif
            </div>
            @error('profile_photo')
                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        @if (auth()->user()->hasRole('agent'))
            <div>
                <flux:input id="phone" name="phone" type="text" label="{{ __('Phone Number') }}" class="mt-1 block w-full" wire:model="phone" autocomplete="tel" />
                @error('phone') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6">
                <flux:input id="agency_name" name="agency_name" type="text" label="{{ __('Agency Name') }}" class="mt-1 block w-full" wire:model="agency_name" />
                @error('agency_name') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6">
                <flux:input id="license_number" name="license_number" type="text" label="{{ __('License Number') }}" class="mt-1 block w-full" wire:model="license_number" />
                @error('license_number') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>
        @endif

        @if (auth()->user()->hasRole('seeker'))
            <div class="mt-6">
                <flux:input id="budget_min" name="budget_min" type="number" label="{{ __('Minimum Budget') }}" class="mt-1 block w-full" wire:model="budget_min" />
                @error('budget_min') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6">
                <flux:input id="budget_max" name="budget_max" type="number" label="{{ __('Maximum Budget') }}" class="mt-1 block w-full" wire:model="budget_max" />
                @error('budget_max') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6">
                <flux:input id="search_radius_km" name="search_radius_km" type="number" label="{{ __('Search Radius (km)') }}" class="mt-1 block w-full" wire:model="search_radius_km" />
                @error('search_radius_km') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">{{ __('Search Location') }}</label>
                <div id="searchLocationMap" style="height: 300px;" class="mt-2 rounded-lg border border-gray-300 shadow-sm"
                     data-latitude="{{ $search_latitude ?: 0 }}"
                     data-longitude="{{ $search_longitude ?: 0 }}"></div>
                <div class="mt-2 grid grid-cols-2 gap-4">
                    <flux:input id="search_latitude" name="search_latitude" type="number" step="0.000001" label="{{ __('Latitude') }}" class="mt-1 block w-full" wire:model="search_latitude" />
                    <flux:input id="search_longitude" name="search_longitude" type="number" step="0.000001" label="{{ __('Longitude') }}" class="mt-1 block w-full" wire:model="search_longitude" />
                </div>
                <p class="mt-1 text-xs text-gray-500">{{ __('Drag the marker on the map to set your search center location.') }}</p>
            </div>

            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Notification Preferences') }}</label>
                <flux:input id="notify_property_type" name="notify_property_type" type="text" label="{{ __('Preferred Property Type') }}" class="mt-1 block w-full" wire:model="notify_property_type" placeholder="e.g., Residential, Commercial, Land" />
                @error('notify_property_type') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div class="mt-6 grid grid-cols-2 gap-4">
                <flux:input id="notify_budget_min" name="notify_budget_min" type="number" label="{{ __('Notification Budget Min') }}" class="mt-1 block w-full" wire:model="notify_budget_min" />
                <flux:input id="notify_budget_max" name="notify_budget_max" type="number" label="{{ __('Notification Budget Max') }}" class="mt-1 block w-full" wire:model="notify_budget_max" />
                @error('notify_budget_min') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
                @error('notify_budget_max') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>
        @endif

        @if(auth()->user()->hasRole('agent') || auth()->user()->hasRole('developer') || auth()->user()->hasRole('owner'))
            <div class="mt-6">
                <label for="bio" class="block text-sm font-medium text-gray-700">{{ __('Bio') }}</label>
                <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" wire:model="bio"></textarea>
                @error('bio')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="linkedin_url" name="linkedin_url" type="url" label="{{ __('LinkedIn URL') }}" class="mt-1 block w-full" wire:model="linkedin_url" placeholder="https://linkedin.com/in/username" />
                @error('linkedin_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="facebook_url" name="facebook_url" type="url" label="{{ __('Facebook URL') }}" class="mt-1 block w-full" wire:model="facebook_url" placeholder="https://facebook.com/username" />
                @error('facebook_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mt-6">
                <flux:input id="twitter_url" name="twitter_url" type="url" label="{{ __('Twitter URL') }}" class="mt-1 block w-full" wire:model="twitter_url" placeholder="https://twitter.com/username" />
                @error('twitter_url')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        @endif

        <div class="flex items-center gap-4">
            <flux:button variant="primary">{{ __('Save') }}</flux:button>

            <x-action-message class="me-3" on="profile-updated">
                {{ __('Saved.') }}
            </x-action-message>
        </div>
    </form>
</section>