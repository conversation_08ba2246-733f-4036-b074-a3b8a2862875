<div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
    <h2 class="text-2xl font-semibold text-gray-800 mb-6">My Property Listings</h2>

    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    @if ($properties->isEmpty())
        <div class="bg-white shadow-md rounded-lg p-8 text-center text-gray-600">
            <p>You haven't listed any properties yet. <a href="{{ route('agent.properties.create') }}" wire:navigate class="text-indigo-600 hover:text-indigo-900">List your first property!</a></p>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach ($properties as $property)
                <div class="bg-white rounded-lg shadow-md overflow-hidden" wire:key="my-property-{{ $property->id }}">
                    @if ($property->hasMedia('gallery'))
                        <img src="{{ $property->getFirstMediaUrl('gallery', 'preview') }}" alt="{{ $property->title }}" class="w-full h-48 object-cover">
                    @else
                        <img src="https://via.placeholder.com/400x300?text=No+Image" alt="No Image" class="w-full h-48 object-cover">
                    @endif
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-semibold text-gray-800">{{ $property->title }}</h3>
                            <span class="px-3 py-1 rounded-full text-sm font-medium
                                @if($property->status->value === 'published') bg-green-100 text-green-800
                                @elseif($property->status->value === 'draft') bg-yellow-100 text-yellow-800
                                @elseif($property->status->value === 'sold' || $property->status->value === 'rented') bg-red-100 text-red-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ ucfirst($property->status->value) }}
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">{{ $property->address_line_1 }}, {{ $property->city }}</p>
                        <p class="text-2xl font-bold text-indigo-600 mb-4">{{ number_format($property->price) }} {{ $property->currency }}</p>

                        <div class="flex space-x-2 text-sm text-gray-500 mb-4">
                            @if (isset($property->features['bedrooms']))
                                <span>{{ $property->features['bedrooms'] }} Beds</span>
                            @endif
                            @if (isset($property->features['bathrooms']))
                                <span>&bull; {{ $property->features['bathrooms'] }} Baths</span>
                            @endif
                            @if (isset($property->features['square_footage']))
                                <span>&bull; {{ $property->features['square_footage'] }} sqft</span>
                            @endif
                            @if (isset($property->features['plot_size']))
                                <span>&bull; {{ $property->features['plot_size'] }} sqft plot</span>
                            @endif
                        </div>

                        <div class="flex justify-end space-x-2">
                            <a href="{{ route('agent.properties.edit', $property) }}" wire:navigate class="inline-flex items-center px-4 py-2 bg-blue-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-600 focus:bg-blue-600 active:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Edit
                            </a>
                            <flux:button variant="danger" wire:click="deleteProperty({{ $property->id }})" wire:confirm="Are you sure you want to delete this property?">
                                Delete
                            </flux:button>
                            @if ($property->status->value === 'published')
                                <flux:button variant="outline" wire:click="updatePropertyStatus({{ $property->id }}, 'sold')" wire:confirm="Mark as Sold?">
                                    Mark Sold
                                </flux:button>
                            @else
                                <flux:button variant="primary" wire:click="updatePropertyStatus({{ $property->id }}, 'published')" wire:confirm="Publish this property?">
                                    Publish
                                </flux:button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="mt-8">
            {{ $properties->links() }}
        </div>
    @endif
</div>
