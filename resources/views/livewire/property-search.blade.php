<!-- Wrap entire component in x-data for state management -->
<div x-data="searchView()" x-init="initProperties({{ json_encode($propertiesData) }})">


    <!-- Search Header -->
    <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div class="md:col-span-3 lg:col-span-2">

                <flux:field>
                    <flux:input wire:model.live.debounce.500ms="keywords" placeholder="Search by location..." />
                </flux:field>
            </div>
            <flux:select wire:model.live="property_type_id">
                <option value="">All Types</option>
                @foreach ($propertyTypes as $type)
                    <option value="{{ $type->id }}">{{ $type->name }}</option>
                @endforeach
            </flux:select>
            <flux:select wire:model.live="property_sub_type_id" :disabled="!$property_type_id">
                <option value="">All Sub-types</option>
                @foreach ($propertySubTypes as $subType)
                    <option value="{{ $subType->id }}">{{ $subType->name }}</option>
                @endforeach
            </flux:select>
            <flux:select wire:model.live="listing_type">
                <option value="">For Sale & Rent</option>
                <option value="for_sale">For Sale</option>
                <option value="for_rent">For Rent</option>
            </flux:select>
            <flux:field>
                <flux:input wire:model.live.debounce.300ms="min_price" placeholder="Min Price" type="number" />
            </flux:field>
            <flux:field>
                <flux:input wire:model.live.debounce.300ms="max_price" placeholder="Max Price" type="number" />
            </flux:field>
            <flux:button wire:click="search" variant="primary" icon="magnifying-glass">
                Search
            </flux:button>
            <flux:button wire:click="resetFilters" variant="outline">
                Clear Filters
            </flux:button>
        </div>
    </div>

    <!-- Results Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Search Results</h1>
            <p class="text-gray-600">{{ $total_properties }} properties found</p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="relative">
                <select wire:model.live="sort_by" class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="created_at">Newest</option>
                    <option value="price">Price</option>
                    <option value="title">Name</option>
                </select>
            </div>
            <div class="relative">
                <select wire:model.live="sort_direction" class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                </select>
            </div>
            <div class="inline-flex rounded-md shadow-sm" role="group">
                <!-- View mode buttons - fixed class binding -->
                <div class="inline-flex rounded-md shadow-sm" role="group">
                    <button @click="viewMode = 'grid'"
                        :class="{
                                'bg-blue-600 text-white': viewMode === 'grid',
                                'text-gray-700 hover:text-blue-600 bg-white border border-gray-200': viewMode !== 'grid'
                            }"
                        class="px-4 py-2 text-sm font-medium rounded-l-lg transition-colors duration-200">
                        <i class="fas fa-squares-2x2 mr-1"></i>
                        <span class="hidden sm:inline">Grid</span>
                    </button>

                    <button @click="viewMode = 'list'"
                        :class="{
                                'bg-blue-600 text-white': viewMode === 'list',
                                'text-gray-700 hover:text-blue-600 bg-white border-t border-b border-gray-200': viewMode !== 'list'
                            }"
                        class="px-4 py-2 text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-list-bullet mr-1"></i>
                        <span class="hidden sm:inline">List</span>
                    </button>

                    <button @click="viewMode = 'map'"
                        :class="{
                                'bg-blue-600 text-white': viewMode === 'map',
                                'text-gray-700 hover:text-blue-600 bg-white border border-gray-200': viewMode !== 'map'
                            }"
                        class="px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200">
                        <i class="fas fa-map mr-1"></i>
                        <span class="hidden sm:inline">Map</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Property Listings -->
    <div wire:loading.class="opacity-50" class="transition-opacity duration-300">
        <!-- Map view -->
        <div x-show="viewMode === 'map'" x-cloak class="mb-6">
            <div id="searchMap"
                style="height: 500px;"
                class="rounded-lg border border-gray-300 shadow-sm"
                wire:ignore></div>
        </div>


        @if ($properties->isEmpty())
        <div x-show="viewMode !== 'map'" class="bg-white shadow-md rounded-lg p-8 text-center text-gray-600">
            <p>No properties found matching your criteria.</p>
        </div>
        @else
        <div x-show="viewMode === 'grid'">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach ($properties as $property)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" wire:click="showProperty({{ $property->id }})" wire:key="property-grid-{{ $property->id }}">
                    <div class="relative">
                        @if ($property->hasMedia('gallery'))
                            <img src="{{ $property->getFirstMediaUrl('gallery', 'preview') }}" alt="{{ $property->title }}" class="w-full h-48 object-cover">
                        @else
                            <img src="https://via.placeholder.com/400x300?text=No+Image" alt="No Image" class="w-full h-48 object-cover">
                        @endif
                        <div class="absolute top-3 left-3 right-3 flex justify-between items-start">
                            <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                            <livewire:favorite-button :property="$property" :key="'fav-grid-'.$property->id" />
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-bold text-gray-900 mb-1">{{ $property->title }}</h3>
                        <p class="text-gray-600 text-sm mb-2">{{ $property->city }}, {{ $property->state_region }}</p>
                        <div class="text-xl font-bold text-blue-600 mb-3">
                            ${{ number_format($property->price) }}
                            <span class="text-sm text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 space-x-3">
                            @if ($property->bedrooms)
                                <div class="flex items-center">
                                    <i class="fas fa-bed mr-1"></i>
                                    <span>{{ $property->bedrooms }}</span>
                                </div>
                            @endif
                            @if ($property->bathrooms)
                                <div class="flex items-center">
                                    <i class="fas fa-bath mr-1"></i>
                                    <span>{{ $property->bathrooms }}</span>
                                </div>
                            @endif
                            @if ($property->square_footage)
                                <div class="flex items-center">
                                    <i class="fas fa-ruler-combined mr-1"></i>
                                    <span>{{ number_format($property->square_footage) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        <div x-show="viewMode === 'list'">
            <div class="space-y-6">
                @foreach ($properties as $property)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" wire:click="showProperty({{ $property->id }})" wire:key="property-list-{{ $property->id }}">
                    <div class="md:flex">
                        <div class="md:w-1/3">
                            @if ($property->hasMedia('gallery'))
                                <img src="{{ $property->getFirstMediaUrl('gallery', 'preview') }}" alt="{{ $property->title }}" class="w-full h-48 md:h-full object-cover">
                            @else
                                <img src="https://via.placeholder.com/400x300?text=No+Image" alt="No Image" class="w-full md:w-1/3 h-48 md:h-full object-cover">
                            @endif
                        </div>
                        <div class="md:w-2/3 p-6">
                            <div class="flex justify-between items-start mb-2">
                                <div class="flex items-center gap-2">
                                    <h3 class="text-xl font-bold text-gray-900">{{ $property->title }}</h3>
                                    <livewire:favorite-button :property="$property" :key="'fav-list-'.$property->id" />
                                </div>
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                            </div>
                            <p class="text-gray-600 mb-3">{{ $property->city }}, {{ $property->state_region }}</p>
                            <p class="text-gray-700 mb-4 line-clamp-3">{{ $property->description }}</p>
                            <div class="flex justify-between items-center">
                                <div class="text-2xl font-bold text-blue-600">
                                    ${{ number_format($property->price) }}
                                    <span class="text-sm text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 space-x-4">
                                    @if ($property->bedrooms)
                                        <div class="flex items-center">
                                            <i class="fas fa-bed mr-1"></i>
                                            <span>{{ $property->bedrooms }}</span>
                                        </div>
                                    @endif
                                    @if ($property->bathrooms)
                                        <div class="flex items-center">
                                            <i class="fas fa-bath mr-1"></i>
                                            <span>{{ $property->bathrooms }}</span>
                                        </div>
                                    @endif
                                    @if ($property->square_footage)
                                        <div class="flex items-center">
                                            <i class="fas fa-ruler-combined mr-1"></i>
                                            <span>{{ number_format($property->square_footage) }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <div class="mt-8">
            {{ $properties->links() }}
        </div>
    </div>
</div>

@push('scripts')
<script>
    function searchView() {
        return {
            viewMode: localStorage.getItem('viewMode') || 'grid',
            mapInstance: null,
            mapMarkers: [],
            properties: [],
            
            initProperties(initialProperties) {
                this.properties = initialProperties;
                
                if (this.viewMode === 'map') {
                    this.$nextTick(() => this.initSearchMap());
                }
                
                this.$watch('viewMode', (value) => {
                    localStorage.setItem('viewMode', value);
                    if (value === 'map') {
                        this.$nextTick(() => this.initSearchMap());
                    } else if (this.mapInstance) {
                        this.cleanupMap();
                    }
                });
            },
            
            initSearchMap() {
                const mapElement = document.getElementById('searchMap');
                if (!mapElement) return;
                
                // Check if map is already initialized
                if (this.mapInstance) {
                    this.updateMapMarkers();
                    return;
                }

                // Initialize new map
                this.mapInstance = L.map(mapElement).setView([39.8283, -98.5795], 4);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(this.mapInstance);

                this.updateMapMarkers();
            },
            
            updateMapMarkers() {
                if (!this.mapInstance) return;
                
                // Clear existing markers
                this.mapMarkers.forEach(marker => this.mapInstance.removeLayer(marker));
                this.mapMarkers = [];
                
                // Add new markers
                let hasValidMarkers = false;
                this.properties.forEach(property => {
                    const lat = parseFloat(property.latitude);
                    const lng = parseFloat(property.longitude);
                    
                    if (!isNaN(lat) && !isNaN(lng)) {
                        hasValidMarkers = true;
                        const marker = L.marker([lat, lng]).addTo(this.mapInstance)
                            .bindPopup(`
                                <b>${property.title}</b><br>
                                <a href="/properties/${property.id}" class="text-blue-600 hover:underline">View Details</a>
                            `);
                        this.mapMarkers.push(marker);
                    }
                });

                if (hasValidMarkers) {
                    const group = new L.featureGroup(this.mapMarkers);
                    this.mapInstance.fitBounds(group.getBounds().pad(0.3));
                } else {
                    this.setDefaultView();
                }
            },
            
            setDefaultView() {
                if (!this.mapInstance) return;
                
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        position => {
                            this.mapInstance.setView(
                                [position.coords.latitude, position.coords.longitude],
                                12
                            );
                        },
                        () => {
                            this.mapInstance.setView([20, 0], 2);
                        }
                    );
                } else {
                    this.mapInstance.setView([20, 0], 2);
                }
            },
            
            cleanupMap() {
                if (this.mapInstance) {
                    // Remove markers
                    this.mapMarkers.forEach(marker => this.mapInstance.removeLayer(marker));
                    this.mapMarkers = [];
                    
                    // Remove map
                    this.mapInstance.remove();
                    this.mapInstance = null;
                }
            }
        }
    }

    // Initialize after Livewire is loaded
    document.addEventListener('livewire:init', () => {
        Livewire.on('propertiesUpdated', (event) => {
            const properties = event.properties || event;
            const el = document.querySelector('[x-data]');
            if (el && el.__x && el.__x.$data) {
                el.__x.$data.properties = properties;

                // Only update markers if map is already initialized
                if (el.__x.$data.viewMode === 'map' && el.__x.$data.mapInstance) {
                    el.__x.$data.updateMapMarkers();
                }
            }
        });
    });

    // Handle navigation events
    document.addEventListener('livewire:navigating', () => {
        const el = document.querySelector('[x-data]');
        if (el && el.__x && el.__x.$data.mapInstance) {
            setTimeout(() => {
                el.__x.$data.mapInstance.invalidateSize();
            }, 100);
        }
    });
</script>
@endpush
