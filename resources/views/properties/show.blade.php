<x-app-layout>
    <!-- Property Detail Page -->
    <div class="fade-in py-8 relative z-10" x-data="{ currentImageIndex: 0, selectedProperty: @js($property) }" x-cloak>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <button @click="window.location.href='{{ route('properties.index') }}'" class="mb-6 text-blue-600 hover:text-blue-700 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Search
            </button>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Image Gallery -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="relative">
                            @if ($property->hasMedia('gallery'))
                                <img x-bind:src="selectedProperty.media[currentImageIndex].original_url" alt="{{ $property->title }}" class="w-full h-96 object-cover">
                                <div class="absolute inset-0 flex items-center justify-between p-4" x-show="selectedProperty.media.length > 1">
                                    <button @click="currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : selectedProperty.media.length - 1" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button @click="currentImageIndex = currentImageIndex < selectedProperty.media.length - 1 ? currentImageIndex + 1 : 0" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            @else
                                <div class="w-full h-96 bg-gray-300 flex items-center justify-center text-gray-500 rounded-lg shadow-md">No Image Available</div>
                            @endif
                        </div>
                        @if ($property->hasMedia('gallery') && count($property->getMedia('gallery')) > 1)
                            <div class="p-4">
                                <div class="flex space-x-2 overflow-x-auto">
                                    @foreach ($property->getMedia('gallery') as $index => $media)
                                        <img src="{{ $media->getUrl('thumb') }}"
                                             class="w-20 h-16 object-cover rounded-lg cursor-pointer opacity-60 hover:opacity-100 transition-opacity"
                                             :class="currentImageIndex === {{ $index }} ? 'opacity-100 ring-2 ring-blue-500' : ''"
                                             @click="currentImageIndex = {{ $index }}">
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Property Details -->
                    <div class="bg-white rounded-2xl shadow-lg p-8">
                        <div class="flex justify-between items-start mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $property->title }}</h1>
                                <p class="text-xl text-gray-600">{{ $property->city }}, {{ $property->state_region }}</p>
                            </div>
                            <div class="flex items-center gap-4">
                                <span class="bg-blue-600 text-white px-4 py-2 rounded-full font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                                <livewire:favorite-button :property="$property" :key="'fav-'.$property->id" />
                            </div>
                        </div>

                        <div class="text-3xl font-bold text-blue-600 mb-6">
                            ${{ number_format($property->price) }}
                            <span class="text-lg text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                        </div>

                        <div class="border-t border-b border-gray-200 py-6 mb-8">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Property Type</span>
                                <span class="font-bold text-gray-900">{{ $property->propertySubType->propertyType->name }}</span>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <span class="text-gray-600">Sub-type</span>
                                <span class="font-bold text-gray-900">{{ $property->propertySubType->name }}</span>
                            </div>
                        </div>

                        <div class="mb-8">
                            @switch($property->propertySubType->propertyType->name)
                                @case('Residential')
                                    @include('properties.partials._details-residential', ['property' => $property])
                                    @break
                                @case('Commercial')
                                    @include('properties.partials._details-commercial', ['property' => $property])
                                    @break
                                @case('Land')
                                    @include('properties.partials._details-land', ['property' => $property])
                                    @break
                                @default
                                    <p>Details for this property type are not available.</p>
                            @endswitch
                        </div>

                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Description</h3>
                            <p class="text-gray-700 leading-relaxed">{{ $property->description }}</p>
                        </div>

                        @if ($property->amenities->count() > 0)
                            <div class="mb-8">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Amenities</h3>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                    @foreach ($property->amenities as $amenity)
                                        <div class="flex items-center text-gray-700">
                                            <i class="fas fa-check-circle text-blue-600 mr-2"></i>
                                            <span>{{ $amenity->name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Map Display -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Location</h3>
                            @if($property->latitude && $property->longitude)
                            <div id="propertyMap"
                                style="height: 300px;"
                                class="rounded-lg border border-gray-300 shadow-sm"
                                data-latitude="{{ $property->latitude }}"
                                data-longitude="{{ $property->longitude }}"
                                data-title="{{ e(Str::limit($property->title, 30)) }}"></div>
                            @else
                            <div class="bg-gray-100 h-64 rounded-lg flex items-center justify-center text-gray-500">
                                Map data not available for this property.
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Contact Card -->
                    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Agent</h3>
                        <div class="flex items-center mb-4">
                            @if($property->user->getFirstMediaUrl('profile_photo'))
                                <img src="{{ $property->user->getFirstMediaUrl('profile_photo') }}" alt="{{ $property->user->name }}" class="w-12 h-12 rounded-full object-cover mr-3">
                            @else
                                <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                                    <span>{{ $property->user->name[0] }}</span>
                                </div>
                            @endif
                            <div>
                                <div class="font-medium text-gray-900">{{ $property->user->name }}</div>
                                <div class="text-sm text-gray-600">Property {{ $property->lister_type ?: 'Agent' }}</div>
                            </div>
                        </div>
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-phone w-5"></i>
                                <span>{{ $property->user->phone }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-envelope w-5"></i>
                                <span>{{ $property->user->email }}</span>
                            </div>
                        </div>

                        <button
                            x-data
                            @click="$dispatch('open-contact-modal', { propertyId: {{ $property->id }} })"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                            wire:loading.attr="disabled"
                            wire:target="open-contact-modal"
                        >
                            <span wire:loading.remove wire:target="open-contact-modal">
                                <i class="fas fa-envelope mr-2"></i>
                                Send Message
                            </span>
                            <span wire:loading wire:target="open-contact-modal">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Loading...
                            </span>
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-heart mr-2 "></i>
                                Save Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-share mr-2"></i>
                                Share Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-calculator mr-2"></i>
                                Mortgage Calculator
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    @if($property->latitude && $property->longitude)
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.mapInitialized) return;
            window.mapInitialized = true;
            
            const mapElement = document.getElementById('propertyMap');
            if (mapElement) {
                const lat = parseFloat(mapElement.dataset.latitude);
                const lng = parseFloat(mapElement.dataset.longitude);
                const title = mapElement.dataset.title;

                if (isNaN(lat) || isNaN(lng)) {
                    console.error('Invalid latitude or longitude for property map.');
                    mapElement.innerHTML = '<div class="p-4 text-center text-gray-500">Error: Invalid map coordinates provided.</div>';
                    return;
                }

                const map = L.map(mapElement).setView([lat, lng], 15); // Zoom level 15

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                L.marker([lat, lng]).addTo(map)
                    .bindPopup(title)
                    .openPopup();
            }
        });
    </script>
    @endif
    @endpush

    <livewire:contact-lister wire:init />
</x-app-layout>
