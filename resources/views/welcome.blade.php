<x-app-layout>
    <!-- Hero Section -->
    <section class="gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Find Your Perfect
                    <span class="block">Dream Home</span>
                </h1>
                <p class="text-xl md:text-2xl mb-12 text-blue-100">
                    Discover amazing properties with our modern, intuitive platform
                </p>

                <!-- Search Bar -->
                <div class="max-w-4xl mx-auto bg-white p-6 rounded-2xl shadow-2xl">
                    <form action="{{ route('properties.index') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="md:col-span-2">
                            <input
                                type="text"
                                name="keywords"
                                placeholder="Search by location, property type..."
                                value="{{ request('keywords') }}"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                            >
                        </div>
                        <select name="property_type_id" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                            <option value="">All Types</option>
                            @foreach($propertyTypes as $type)
                                <option value="{{ $type->id }}" {{ request('property_type_id') == $type->id ? 'selected' : '' }}>
                                    {{ $type->name }}
                                </option>
                            @endforeach
                        </select>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

<!-- Popular Cities -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Cities</h2>
            <p class="text-xl text-gray-600">Explore properties in the most sought-after locations</p>
        </div>



<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-6">
    @foreach($popularCities as $city)
    <div class="relative overflow-hidden rounded-2xl shadow-lg group h-64 city-card">
        <!-- Background image placeholder - replace with actual city images -->
        <div class="absolute inset-0 bg-gray-200 rounded-xl w-full h-full flex items-center justify-center">
            <i class="fas fa-city text-6xl text-gray-400"></i>
        </div>
        <!-- Dark overlay that slides up on hover -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <!-- City name (always visible) -->
        <div class="absolute inset-0 flex items-center justify-center z-10">
            <h3 class="text-2xl font-bold text-white text-center px-4">{{ $city->city }}</h3>
        </div>
        <!-- Details that slide up on hover -->
        <div class="absolute bottom-0 left-0 right-0 p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 z-20">
            <h3 class="text-xl font-bold text-white">{{ $city->city }}</h3>
            <p class="text-white/90">{{ $city->count }} properties</p>
            <a href="{{ route('properties.index', ['keywords' => $city->city]) }}" class="mt-2 inline-block text-blue-300 hover:text-white transition-colors">
                Browse properties <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    @endforeach
</div>
    </div>
</section>

<!-- Featured Properties -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
            <p class="text-xl text-gray-600">Discover our handpicked selection of premium properties</p>
        </div>



        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
@forelse($featuredProperties as $property)
<div class="relative overflow-hidden rounded-2xl shadow-lg group h-96">
@if($property->hasMedia('gallery'))
    <img src="{{ $property->getFirstMediaUrl('gallery', 'preview') }}" alt="{{ $property->title }}" class="w-full h-full object-cover">
@else
    <div class="w-full h-full bg-gray-200 flex items-center justify-center">
        <i class="fas fa-home text-4xl text-gray-400"></i>
    </div>
@endif
    
    <!-- Dark overlay on hover -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    
    <!-- Top badges -->
    <div class="absolute top-4 left-4 z-10">
        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
    </div>
    
    <!-- Property details that slide up on hover -->
    <div class="absolute bottom-0 left-0 right-0 p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 z-20">
        <h3 class="text-xl font-bold text-white">{{ $property->title }}</h3>
        <p class="text-white/90">{{ $property->city }}, {{ $property->state_region }}</p>
        <div class="flex items-center justify-between mt-2">
            <div class="text-lg font-bold text-white">
                ${{ number_format($property->price) }}
                @if($property->listing_type === 'for_rent')
                    <span class="text-sm text-white/80">/month</span>
                @endif
            </div>
        </div>
        @if($property->bedrooms || $property->bathrooms || $property->square_footage)
            <div class="mt-3 flex items-center text-sm text-white/80 space-x-3">
                @if($property->bedrooms)
                    <div class="flex items-center">
                        <i class="fas fa-bed mr-1"></i>
                        <span>{{ $property->bedrooms }}</span>
                    </div>
                @endif
                @if($property->bathrooms)
                    <div class="flex items-center">
                        <i class="fas fa-bath mr-1"></i>
                        <span>{{ $property->bathrooms }}</span>
                    </div>
                @endif
                @if($property->square_footage)
                    <div class="flex items-center">
                        <i class="fas fa-ruler-combined mr-1"></i>
                        <span>{{ number_format($property->square_footage) }}</span> sqft
                    </div>
                @endif
            </div>
        @endif
        <div class="mt-4">
            <a href="{{ route('properties.show', $property) }}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                View Details
            </a>
        </div>
    </div>
    
    <!-- Minimal info always visible -->
    <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white transition-opacity duration-200 group-hover:opacity-0">
        <h3 class="text-lg font-bold truncate">{{ $property->title }}</h3>
        <p class="text-white/80">${{ number_format($property->price) }} @if($property->listing_type === 'for_rent')<span class="text-sm">/month</span>@endif</p>
    </div>
</div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-home text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No Properties Available</h3>
                        <p class="text-gray-500">Check back soon for new listings!</p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-10">
                <a href="{{ route('properties.index') }}" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                    View All Properties
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Ready to List Your Property?</h2>
            <p class="text-xl text-gray-600 mb-8">Join thousands of property owners and agents using Lokus</p>
            @auth
                @if(auth()->user()->hasRole('agent'))
                    <a href="{{ route('agent.properties.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                        Start Listing Today
                    </a>
                @else
                    <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                        Join as an Agent
                    </a>
                @endif
            @else
                <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                    Start Listing Today
                </a>
            @endauth
        </div>
    </section>
</x-app-layout>
